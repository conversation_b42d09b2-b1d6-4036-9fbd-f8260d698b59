const fs = require('fs');

function generateFormTemplate(obj) {
    const formItems = [];
    const dataProperties = [];
    const rules = [];

    for (const [key, label] of Object.entries(obj)) {
        formItems.push(`
        <el-descriptions-item>
            <template slot="label">
                ${label}
            </template>
            <el-form-item prop="${key}">
                <el-input v-model="formData.${key}" placeholder="请输入${label}"></el-input>
            </el-form-item>
        </el-descriptions-item>
    `);

        dataProperties.push(`"${key}": "",`);
        rules.push(`
        "${key}": [
            { required: true, message: '请输入${label}', trigger: 'blur' }
        ],`);
    }

    const formItemStr = formItems.join('');
    const dataPropertiesStr = dataProperties.join('\n                    ');
    const rulesStr = rules.join('\n                    ');

    return `
<template>
    <div>
      <el-form ref="ruleForm" :model="formData" label-width="120px" :rules="rules">
          ${formItemStr}
            <el-form-item>
                <el-button type="primary" @click="submitForm('ruleForm')">立即创建</el-button>
                <el-button @click="resetForm('ruleForm')">重置</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
    export default {
        data() {
            return {
                formData: {
                    ${dataPropertiesStr}
                },
                rules: {
                    ${rulesStr}
                }
            }
        },
        methods: {
            submitForm(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        alert('submit!');
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            resetForm(formName) {
                this.$refs[formName].resetFields();
            }
        }
    };
</script>
<style scoped></style>
`;
}

// 示例输入对象
const inputObject = {
    nickName: "昵称",
    oaCode: "OA号",
    teamId: "所属团队ID",
    avatar: "头像",
    teamName: "所属团队名称",
    bust: "半身像",
    name: "真实姓名",
    enName: "英文名称/拼音",
    cellPhone: "手机号码",
    email: "电子邮箱",
    company: "所属公司",
    administrativeRank: "行政职级",
    professionalRank: "专业职级",
    profile: "简介",
    post: "职位",
    school: "毕业院校",
    research: "研究领域",
    experience: "项目经验",
    honoraryBrief: "资质荣誉",
    personalProfile: "个人简介",
    results: "研究成果",
    honoraryDetail: "资质荣誉详情",
    attachment: "word附件"

};

// 生成表单模板
const formTemplate = generateFormTemplate(inputObject);

fs.writeFile('GeneratedForm.vue', formTemplate, (err) => {
    if (err) {
        console.error(err);
    } else {
        console.log('GeneratedForm.vue 文件已生成');
    }
});
