# 路由自动生成脚本

这个脚本用于自动扫描 `src/views` 目录下的所有 Vue 文件，并生成相应的路由配置。

## 使用方法

1. 通过 npm 脚本运行：

```bash
npm run generate-routes
```

或者使用 yarn：

```bash
yarn generate-routes
```

2. 直接执行脚本：

```bash
node scripts/generate-routes.js
```

## 功能介绍

- 自动扫描 `src/views` 目录及其子目录中的所有 `.vue` 文件
- 根据文件路径生成路由路径，例如：
  - `src/views/HomeView.vue` → 路由 `/HomeView`
  - `src/views/new/buttonView.vue` → 路由 `/new/buttonView`
- 生成惰性加载的路由配置（使用 webpack 动态导入）
- 自动生成完整的 `routes.js` 文件
- 自动备份原有路由配置到 `src/router/backups` 目录

## 注意事项

- 脚本会**覆盖** `src/router/routes.js` 文件，但会在执行前自动创建备份
- 备份文件保存在 `src/router/backups` 目录，使用时间戳命名
- 路由名称与组件文件名保持一致（不含 `.vue` 扩展名）
- 自动忽略非 Vue 文件（如 JavaScript 文件、图片等）

## 恢复备份

如需恢复之前的路由配置，可以从 `src/router/backups` 目录复制备份文件到 `src/router/routes.js`：

```bash
cp src/router/backups/routes-[timestamp].js src/router/routes.js
``` 