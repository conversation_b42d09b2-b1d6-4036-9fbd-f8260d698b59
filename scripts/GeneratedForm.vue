
<template>
    <div>
      <el-form ref="ruleForm" :model="formData" label-width="120px" :rules="rules">
          
        <el-descriptions-item>
            <template slot="label">
                昵称
            </template>
            <el-form-item prop="nickName">
                <el-input v-model="formData.nickName" placeholder="请输入昵称"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                OA号
            </template>
            <el-form-item prop="oaCode">
                <el-input v-model="formData.oaCode" placeholder="请输入OA号"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                所属团队ID
            </template>
            <el-form-item prop="teamId">
                <el-input v-model="formData.teamId" placeholder="请输入所属团队ID"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                头像
            </template>
            <el-form-item prop="avatar">
                <el-input v-model="formData.avatar" placeholder="请输入头像"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                所属团队名称
            </template>
            <el-form-item prop="teamName">
                <el-input v-model="formData.teamName" placeholder="请输入所属团队名称"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                半身像
            </template>
            <el-form-item prop="bust">
                <el-input v-model="formData.bust" placeholder="请输入半身像"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                真实姓名
            </template>
            <el-form-item prop="name">
                <el-input v-model="formData.name" placeholder="请输入真实姓名"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                英文名称/拼音
            </template>
            <el-form-item prop="enName">
                <el-input v-model="formData.enName" placeholder="请输入英文名称/拼音"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                手机号码
            </template>
            <el-form-item prop="cellPhone">
                <el-input v-model="formData.cellPhone" placeholder="请输入手机号码"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                电子邮箱
            </template>
            <el-form-item prop="email">
                <el-input v-model="formData.email" placeholder="请输入电子邮箱"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                所属公司
            </template>
            <el-form-item prop="company">
                <el-input v-model="formData.company" placeholder="请输入所属公司"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                行政职级
            </template>
            <el-form-item prop="administrativeRank">
                <el-input v-model="formData.administrativeRank" placeholder="请输入行政职级"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                专业职级
            </template>
            <el-form-item prop="professionalRank">
                <el-input v-model="formData.professionalRank" placeholder="请输入专业职级"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                简介
            </template>
            <el-form-item prop="profile">
                <el-input v-model="formData.profile" placeholder="请输入简介"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                职位
            </template>
            <el-form-item prop="post">
                <el-input v-model="formData.post" placeholder="请输入职位"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                毕业院校
            </template>
            <el-form-item prop="school">
                <el-input v-model="formData.school" placeholder="请输入毕业院校"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                研究领域
            </template>
            <el-form-item prop="research">
                <el-input v-model="formData.research" placeholder="请输入研究领域"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                项目经验
            </template>
            <el-form-item prop="experience">
                <el-input v-model="formData.experience" placeholder="请输入项目经验"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                资质荣誉
            </template>
            <el-form-item prop="honoraryBrief">
                <el-input v-model="formData.honoraryBrief" placeholder="请输入资质荣誉"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                个人简介
            </template>
            <el-form-item prop="personalProfile">
                <el-input v-model="formData.personalProfile" placeholder="请输入个人简介"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                研究成果
            </template>
            <el-form-item prop="results">
                <el-input v-model="formData.results" placeholder="请输入研究成果"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                资质荣誉详情
            </template>
            <el-form-item prop="honoraryDetail">
                <el-input v-model="formData.honoraryDetail" placeholder="请输入资质荣誉详情"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
        <el-descriptions-item>
            <template slot="label">
                word附件
            </template>
            <el-form-item prop="attachment">
                <el-input v-model="formData.attachment" placeholder="请输入word附件"></el-input>
            </el-form-item>
        </el-descriptions-item>
    
            <el-form-item>
                <el-button type="primary" @click="submitForm('ruleForm')">立即创建</el-button>
                <el-button @click="resetForm('ruleForm')">重置</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
    export default {
        data() {
            return {
                formData: {
                    "nickName": "",
                    "oaCode": "",
                    "teamId": "",
                    "avatar": "",
                    "teamName": "",
                    "bust": "",
                    "name": "",
                    "enName": "",
                    "cellPhone": "",
                    "email": "",
                    "company": "",
                    "administrativeRank": "",
                    "professionalRank": "",
                    "profile": "",
                    "post": "",
                    "school": "",
                    "research": "",
                    "experience": "",
                    "honoraryBrief": "",
                    "personalProfile": "",
                    "results": "",
                    "honoraryDetail": "",
                    "attachment": "",
                },
                rules: {
                    
        "nickName": [
            { required: true, message: '请输入昵称', trigger: 'blur' }
        ],
                    
        "oaCode": [
            { required: true, message: '请输入OA号', trigger: 'blur' }
        ],
                    
        "teamId": [
            { required: true, message: '请输入所属团队ID', trigger: 'blur' }
        ],
                    
        "avatar": [
            { required: true, message: '请输入头像', trigger: 'blur' }
        ],
                    
        "teamName": [
            { required: true, message: '请输入所属团队名称', trigger: 'blur' }
        ],
                    
        "bust": [
            { required: true, message: '请输入半身像', trigger: 'blur' }
        ],
                    
        "name": [
            { required: true, message: '请输入真实姓名', trigger: 'blur' }
        ],
                    
        "enName": [
            { required: true, message: '请输入英文名称/拼音', trigger: 'blur' }
        ],
                    
        "cellPhone": [
            { required: true, message: '请输入手机号码', trigger: 'blur' }
        ],
                    
        "email": [
            { required: true, message: '请输入电子邮箱', trigger: 'blur' }
        ],
                    
        "company": [
            { required: true, message: '请输入所属公司', trigger: 'blur' }
        ],
                    
        "administrativeRank": [
            { required: true, message: '请输入行政职级', trigger: 'blur' }
        ],
                    
        "professionalRank": [
            { required: true, message: '请输入专业职级', trigger: 'blur' }
        ],
                    
        "profile": [
            { required: true, message: '请输入简介', trigger: 'blur' }
        ],
                    
        "post": [
            { required: true, message: '请输入职位', trigger: 'blur' }
        ],
                    
        "school": [
            { required: true, message: '请输入毕业院校', trigger: 'blur' }
        ],
                    
        "research": [
            { required: true, message: '请输入研究领域', trigger: 'blur' }
        ],
                    
        "experience": [
            { required: true, message: '请输入项目经验', trigger: 'blur' }
        ],
                    
        "honoraryBrief": [
            { required: true, message: '请输入资质荣誉', trigger: 'blur' }
        ],
                    
        "personalProfile": [
            { required: true, message: '请输入个人简介', trigger: 'blur' }
        ],
                    
        "results": [
            { required: true, message: '请输入研究成果', trigger: 'blur' }
        ],
                    
        "honoraryDetail": [
            { required: true, message: '请输入资质荣誉详情', trigger: 'blur' }
        ],
                    
        "attachment": [
            { required: true, message: '请输入word附件', trigger: 'blur' }
        ],
                }
            }
        },
        methods: {
            submitForm(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        alert('submit!');
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            resetForm(formName) {
                this.$refs[formName].resetFields();
            }
        }
    };
</script>
<style scoped></style>
