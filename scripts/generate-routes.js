const fs = require('fs');
const path = require('path');

// Configurations
const viewsDir = path.resolve(__dirname, '../src/views');
const routesFile = path.resolve(__dirname, '../src/router/routes.js');
const backupDir = path.resolve(__dirname, '../src/router/backups');

// Create backup directory if it doesn't exist
if (!fs.existsSync(backupDir)) {
  fs.mkdirSync(backupDir, { recursive: true });
}

// Create a backup of the current routes file
if (fs.existsSync(routesFile)) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupFile = path.join(backupDir, `routes-${timestamp}.js`);
  fs.copyFileSync(routesFile, backupFile);
  console.log(`Created backup at: ${backupFile}`);
}

// Function to get all Vue files recursively
function getVueFiles(dir, subPath = '') {
  const files = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const relativePath = path.join(subPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      files.push(...getVueFiles(fullPath, relativePath));
    } else if (item.endsWith('.vue')) {
      files.push({
        name: item.replace('.vue', ''),
        fullPath,
        relativePath: relativePath.replace(/\\/g, '/'),
        subDir: subPath.replace(/\\/g, '/')
      });
    }
  }
  
  return files;
}

// Get all Vue files
const vueFiles = getVueFiles(viewsDir);

// Generate import statements and routes
let importStatements = [];
let routeDefinitions = [];

vueFiles.forEach(file => {
  // Skip specific files if needed (e.g., worker.js and image files)
  if (file.name.endsWith('.js') || file.name.endsWith('.png') || file.name.endsWith('.jpg')) {
    return;
  }
  
  const componentName = file.name;
  const importPath = `../views/${file.relativePath}`;
  const routePath = `/${file.subDir}/${file.name}`.replace(/\/+/g, '/');
  
  importStatements.push(`const ${componentName} = () =>
  import(/* webpackChunkName: "${componentName}" */ "${importPath}");`);
  
  routeDefinitions.push(`  {
    path: "${routePath}",
    name: "${componentName}",
    component: ${componentName},
  }`);
});

// Create the new routes file content
const newContent = `${importStatements.join('\n\n')}

export const otherRoutes = [
${routeDefinitions.join(',\n')}
];
`;

// Write the new routes file
fs.writeFileSync(routesFile, newContent, 'utf8');

console.log(`Routes generated successfully! Total routes: ${routeDefinitions.length}`); 