const { defineConfig } = require('@vue/cli-service')
const UnoCSS = require('@unocss/webpack').default;
const presetUno = require('@unocss/preset-uno').default;
const presetIcons = require('@unocss/preset-icons').default;
const transformerDirectives = require('@unocss/transformer-directives').default;
const path = require('path');

module.exports = defineConfig({
  // 只转译必要依赖，提升编译速度
  transpileDependencies: [], // 如有需要转译的依赖，填在数组里
  configureWebpack: config => {
    // 开发环境下优化 source map
    if (process.env.NODE_ENV === 'development') {
      config.devtool = 'cheap-module-source-map';
    }
    return {
      plugins: [
        UnoCSS({
          presets: [presetUno(), presetIcons()],
          transformers: [transformerDirectives()],
        }),
      ],
      optimization: {
        realContentHash: true,
      },
      resolve: {
        alias: {
          '@': path.resolve(__dirname, 'src')
        }
      }
    }
  },
  chainWebpack: config => {
    // 开启babel-loader缓存
    config.module
      .rule('js')
      .use('babel-loader')
      .tap(options => {
        options.cacheDirectory = true;
        return options;
      });
  }
});
