self.addEventListener('message', function (e) {
    const poolList = e.data.poolList;
    const localPersonList = e.data.localPersonList;

    poolList.forEach(pool => {
        if (pool.children && pool.children.length > 0) {
            pool.children.forEach(itemA => {
                const isInB = localPersonList.some(itemB => itemB.contactId === itemA.contactId);
                if (itemA.selected && !isInB) {
                    localPersonList.push(itemA);
                } else if (!itemA.selected && isInB) {
                    const index = localPersonList.findIndex(itemB => itemB.contactId === itemA.contactId);
                    if (index > -1) {
                        localPersonList.splice(index, 1);
                    }
                }
            });
        }
    });

    self.postMessage(localPersonList);
});
