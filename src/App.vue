<template>
  <div id="app" ref="container">
    <el-container style="height:100%;">
      <transition name="slide">
        <el-aside width="200px" style="height: 100%" class="aside" v-show="sidebarVisible">
          <el-menu @select="handleSelect" default-active="1" class="el-menu-vertical-demo">
            <!-- <el-menu-item index="/">
              Home
            </el-menu-item> -->
            <el-submenu v-for="(group, groupName) in menuGroups" :key="groupName" :index="groupName">
              <template slot="title">
                <span>{{ groupName }}</span>
              </template>
              <el-menu-item v-for="route in group" :key="route.path" :index="route.path">
                <router-link :to="route.path">{{ route.name }}</router-link>
              </el-menu-item>
            </el-submenu>
          </el-menu>
        </el-aside>
      </transition>
      <el-main>

        <router-view />
      </el-main>
    </el-container>


  </div>
</template>

<script>

import { otherRoutes } from './router/routes';

export default {
  name: 'App',
  data() {
    return {
      sidebarVisible: false,
      menuGroups: {}
    }
  },
  created() {
    this.sidebarVisible = !this.$isMobile;
    this.$nextTick(() => {
      this.$refs.container.style.height = window.innerHeight + 'px';
    });

    // 根据路由路径分组
    this.menuGroups = otherRoutes.reduce((groups, route) => {
      const [, groupName] = route.path.split('/');
      if (!groups[groupName]) {
        groups[groupName] = [];
      }
      groups[groupName].push(route);
      return groups;
    }, {});
  },
  methods: {
    changeSidebarVisible() {

      if (this.$isMobile) {
        this.sidebarVisible = !this.sidebarVisible

      }
    },
    handleSelect(index) {
      this.changeSidebarVisible()
      const targetPath = index;
      if (this.$route.path !== targetPath) {
        this.$router.push({ path: targetPath }).catch(err => {
          // 忽略重复的导航错误
        });
      }
    }
  }
}
</script>

<style lang="scss">
@import "@/mixins";

#app {
  //width: var(--phone-width);
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  //text-align: center;
  color: #2c3e50;
}

.el-menu-item {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.aside {
  @media screen and (max-width:768px) {
    position: fixed;
    z-index: 999;
  }

  @include scrollbar-track-style;
}

.single-menu-btn {
  height: 40px;
  display: flex;
  align-items: center;
}

.fold-menu {
  @media screen and (min-width: 768px) {
    //border: 1px solid red;
    display: none;
  }
}

.el-menu-vertical-demo {
  height: 100%;
}

//动画
.slide-enter-active {
  transition: transform 0.5s ease;
}

.slide-leave-active {
  transition: transform 0.5s ease;
}

.slide-enter,
.slide-leave-to {
  transform: translateX(-100%);
}


.el-main {
  padding: 0 !important;
}
</style>
