import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
// import Vant from 'vant';
// import 'vant/lib/index.css';
import {isMobile} from "@/utils/device";
import eventBus from "@/utils/eventBus";

Vue.prototype.$isMobile = isMobile();

import 'uno.css';
// Vue.use(Vant);
Vue.config.productionTip = false
Vue.use(ElementUI);
new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
