// directives.js
import Vue from 'vue'

Vue.directive("loadmore", {
    bind(el, binding, vnode) {
        const SELECTWRAP = el.querySelector(
            // ".el-select-dropdown .el-select-dropdown__wrap"
            ".el-cascader-panel .el-cascader-menu__wrap"
        );
        SELECTWRAP.addEventListener("scroll", function () {
            // scrollTop 这里可能因为浏览器缩放存在小数点的情况，导致了滚动到底部时
            // scrollHeight 减去滚动到底部时的scrollTop ，依然大于clientHeight 导致无法请求更多数据
            // 这里将scrollTop向上取整 保证滚到底部时，触发调用

            let temp = this.scrollTop;
            const CONDITION = this.scrollHeight - Math.ceil(this.scrollTop) <= this.clientHeight;

            // el.scrollTop !== 0 当输入时，如果搜索结果很少，以至于没看到滚动条，那么此时的CONDITION计算结果是true，会执行bind.value()，此时不应该执行，否则搜索结果不匹配
            if (CONDITION && this.scrollTop !== 0) {
                binding.value();

                console.log('temp',temp)
                setTimeout(() => {
                    this.scrollTop = temp+100;// 滚动加载更多时，显示的数据在上次位置基础上往上滑动100
                }, 0)

            }
        });
    },
});

Vue.directive("selectloadmore", {
    bind(el, binding, vnode) {
        const SELECTWRAP = el.querySelector(
            ".el-select-dropdown .el-select-dropdown__wrap"
        );
        SELECTWRAP.addEventListener("scroll", function () {

            const CONDITION = this.scrollHeight - Math.ceil(this.scrollTop) <= this.clientHeight;

            if (CONDITION && this.scrollTop !== 0) {
                binding.value();


            }
        });
    },
});

// main.js 或组件内部
Vue.directive('line-count', {
    // 当被绑定的元素插入到 DOM 中时……
    inserted: function (el, binding, vnode) {
       Vue.nextTick(() => {
          
           const containerWidth = el.clientWidth
           console.log()
           const nameSpan = el.querySelectorAll('.name')
           let maxOffsetWidth = 0;
           let spanWidthList = []
 
           if (nameSpan && nameSpan.length > 0) {
               spanWidthList = Array.from(nameSpan).map(item => item.offsetWidth+2)
        
           }
           let elementCount = calculateElements(spanWidthList, containerWidth, 2);

           if (elementCount < spanWidthList.length) {
               console.log(`output->放不下`)
               elementCount = calculateElements(spanWidthList, containerWidth, 2, 60);
           }
    
          
           console.log(`elementCount`, elementCount)
           vnode.context.$emit('directive-result', parseInt(elementCount), binding.value);

       })

    },
    // 当指令所在组件的 VNode 及其子 VNode 全部更新后调用
    componentUpdated: function (el, binding, vnode) {
    

    }
});

function calculateElements(widths, containerWidth, rowCount, reduction = 0) {
    let totalWidth = 0;
    let elementCount = 0;
    let rowIndex = 0;

    for (let i = 0; i < widths.length; i++) {
        if (totalWidth + widths[i] <= containerWidth - (rowIndex >= rowCount - 1 ? reduction : 0) && rowIndex < rowCount) {
            totalWidth += widths[i];
            elementCount++;
        } else {
            totalWidth = widths[i];
            rowIndex++;
            if (rowIndex >= rowCount) {
                break;
            }
            elementCount++;
        }
    }

    return elementCount;
}

function cutText(text, length) {
    if (text.length <= length) {
        return text;
    }

    let cutIndex = text.lastIndexOf('，', length);
    if (cutIndex === -1 || cutIndex === length - 1) {
        return text.slice(0, length);
    }

    let result = text.slice(0, cutIndex + 1);
    if (result.endsWith('，')) {
        result = result.slice(0, -1);
    }
    return result;
}
// 计算行数的方法
function calculateLines(el) {
    const style = window.getComputedStyle(el);
    const lineHeight = parseFloat(style.lineHeight);
    const height = parseFloat(style.height);
    const lineCount = Math.round(height / lineHeight);
    console.log(`行数: ${lineCount}`);
    
    return lineCount;
    // 如果你想在元素上存储行数信息，可以这样做：
    el.setAttribute('data-line-count', lineCount);
}


// 计算每行可以显示多少个字符
function calculateCharsPerLine(element) {
    const style = window.getComputedStyle(element);
    const font = style.font; // 或者单独获取'font-size'和'font-family'拼接
    const elementWidth = element.offsetWidth;

    // 假设使用一个常见的字符如'M'来测量
    const charWidth = measureText('的', font);

    // 计算每行大致可以容纳多少个字符
    const charsPerLine = Math.floor(elementWidth / charWidth);
    return charsPerLine;
}


// 创建一个span元素来测量字符宽度
function measureText(text, font) {
    const span = document.createElement('span');
    document.body.appendChild(span);

    // 设置span的字体样式，确保与要测量的元素样式相同
    span.style.font = font;
    span.style.whiteSpace = 'nowrap';
    span.style.visibility = 'hidden'; // 隐藏span元素
    span.textContent = text;

    const width = span.offsetWidth;
    document.body.removeChild(span); // 清理DOM
    return width;
}
