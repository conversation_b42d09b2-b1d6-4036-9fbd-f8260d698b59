<template>
  <div class="">
    <el-date-picker  style="width: 240px;" v-model="value1" type="daterange" range-separator="至" start-placeholder="开始日期"
      end-placeholder="结束日期">
    </el-date-picker>

  </div>
</template>
<script>
export default {
  data() {
    return {
      value1: ''
    };
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    }
  }
};
</script>
<style lang="scss" scoped></style>