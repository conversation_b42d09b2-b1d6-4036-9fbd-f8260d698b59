<!--使用插件实现虚拟滚动-->
<template>
  <RecycleScroller
      class="scroller"
      :items="list"
      :item-size="32"
      key-field="id"
      v-slot="{ item }"
  >
    <div class="user">
      {{ item.name }}
    </div>
  </RecycleScroller>
</template>

<script>
import { RecycleScroller } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

export default {
  components: {RecycleScroller},
  data() {
    return {
      list:[]
    }
  },
  mounted() {
    for (let i = 1; i <= 2; i++) {
      this.list.push({id: i, name: `选项${i}`})
    }
  }
}
</script>

<style scoped>
.scroller {
  border: 1px solid red;
  /* height: 40%; */
  max-height: 200px;
}

.user {
  /* border: 1px solid red; */
  height: 32%;
  padding: 0 12px;
  display: flex;
  align-items: center;
}
</style>
