
<template>
  <div>
    
    <h1>FileNameDemoView</h1>
    <div class="file">
      <template>
        <el-popconfirm title="这是一段内容确定删除吗？" @confirm="confirmDel">
          <el-button slot="reference">删除</el-button>
        </el-popconfirm>
      </template>
      <van-notice-bar color="#d6001a" background="transparent" left-icon="info-o">
        技术是开发它的人的共同灵魂。
      </van-notice-bar>
      <div v-if="title !== ''" class="top cell">
        <span class="title">{{ title }}</span>
        <!-- <span class="add" v-show="status!=='view'">添加</span> -->
      </div>

      <div class="bottom cell">
        <div class="img-box" v-for="(item, index) in fileList" :key="item.id">
          <van-swipe-cell>
            <div class="file-box">
              <img class="doc-png" :src="require('@/assets/doc.png')" @click="downloadFile(item)">
              <div @click="downloadFile" class="name-span">{{ item.fileName }}<van-button class="zhpdf">转换pdf</van-button>
              </div>

            </div>
            <template #right>
              <van-button style="height: 100%;" square type="danger" text="删除" @click="deleteFile(index)" />
            </template>

          </van-swipe-cell>

        </div>

      </div>


    </div>

  </div>
</template>

<script>

import { Dialog } from 'vant';

export default {
  name: 'FileNameDemoView',
  data() {
    return {
      fileLink: '',
      pdfVisible: false,
      excelVisible: false,
      docVisible: false,
      fileList: [
        {
          id: 1,
          fileName: 'flowable接口流程接口流程接口流程-2122122112.docx'
        },
        {
          id: 2,
          fileName: '文件2'
        },
      ],
      title: '附件',
      status: ''

    }
  },
  methods: {
    confirmDel() {
      console.log(`output`)

    },
    deleteFile(index) {
      Dialog.confirm({
        message: '弹窗内容',
      }).then(() => {
        console.log(`output`)
        // this.fileList.splice(index, 1)
      }).catch(() => {
        // on cancel
      });

    },
    downloadFile(item) {


    },
    closeFile() {

    }
  }
};
</script>
<style lang="less" >
@blue: #3c6cfe;

/deep/ .van-dialog__confirm {
  &:active {
    color: blue !important;
  }

  color: blue !important;
}

/deep/ .van-dialog__confirm:active {
  color: blue !important;
}

.file {

  width: 360px;
  border: 1px solid black;

  .header {
    display: flex;
    justify-content: space-between;

    .add {
      font-weight: unset;
      font-size: 14px;
      color: @blue;
    }
  }

  .add {
    color: @blue;
  }

  .top {
    position: relative;

    //margin-top: 1px;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 5%;
      width: 90%;
      height: 1px;
      background-color: #ffffff;
    }

    height: 20px;

  }

  .bottom {

    display: flex;
    flex-direction: column;

    height: unset;

    .img-box {

      width: 100%;
      margin-bottom: 5px;
      display: flex;
      align-items: center;
      position: relative;

      .file-box {

        display: flex;
        align-items: center;
        height: 100px;
        border: 1px solid red;
        .doc-png {

          width: 40px;
          height: 40px;
          margin-right: 10px;
        }


        .name-span {
       width: 500px;
     
          // display: inline-block;
          display: inline-flex;
          word-break: break-all;
          align-items: center;
          border: 1px solid red;
          vertical-align: top;
          .zhpdf {
            min-width: 60px;
          width: 60px;
          
            height: 20px;
            line-height: 20px;
            background: #3c6cfe;
            border-radius: 5px;
            border: none;
            padding: 0 5px;

            span {
              color: #ffffff;
              font-size: 10px;
            }
          }


        }
      }

    }




  }
}

.cell {
  border: 1px solid red;
  background-color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0 10px 15px;
  height: 50px;

  .title {
    margin-bottom: -6px;
    margin-left: 2px;
    font-weight: bold;
    font-size: 14px;
  }

  span {
    font-size: 14px;
    // color: #333;
  }

  .select {
    color: darkgray;
    font-weight: unset;
  }
}
</style>