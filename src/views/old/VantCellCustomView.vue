
<template>
  <div>
    <h1>VantCellCustomView</h1>

    <div class="box" style="width: 400px;">

      <div class="container">
        <div class="header none-bottom">
          <div class="left">
            <span class="h3">
              鱿鱼须
            </span>
          </div>
          <div class="right">
            right
          </div>
          <div class="line"></div>
        </div>


        <van-cell>
          <template #title>
            <span class="box-title">飞机 | 2020-2022</span>
          </template>
        </van-cell>

        <div class="agency-box">
          <van-cell class="agency-box__agency-cell" value-class="" title="路演培训机构">

            <div class="agency-box__agency-cell__agency-item">
              <div class="agency-box__agency-cell__agency-item__title">
                <span class="agency-box__agency-cell__agency-item__title--1">兴业银行成都分行</span>
                <span class="agency-box__agency-cell__agency-item__title--2">福建省</span>

              </div>
              <div class="agency-box__agency-cell__agency-item__addr">福建省福州市台江区江滨中大道398号兴业银行大厦福建省福州市台江区江滨中大道398号兴业银行大厦
              </div>

            </div>

          </van-cell>


          <van-cell class="agency-box__agency-cell-2" value-class="">
            <template #title>
              <div class="agency-box__agency-cell-2__title-box">
                <span class="agency-box__agency-cell-2__title">路演培训机构</span>
                <van-icon name="add-o" class="agency-box__agency-cell-2__icon" color="#3C6CFE" />

              </div>
            </template>


            <div class="agency-box__agency-cell-2__form">
              <van-field v-model="username" name="用户名" label="用户名" placeholder="用户名"
                :rules="[{ required: true, message: '请填写用户名' }]" />
            </div>

          </van-cell>
        </div>
        <van-cell value-class="title-value" is-link>

          <template #title>
            <div>
              <span class="title__text border-solid">单元格</span>
            </div>
          </template>
          <div class="title">
               <span class="title__tag">集团内</span><span>单格格单元单格格单元单格格单元格单元格单元格121121</span>
         
          </div>

        </van-cell>
        <van-cell center title="标题">

          <template #right-icon>
            <div style="color: red" class="switch-box">1212<van-switch v-model="checked" size="24" @click="clickSwitch" />
            </div>
          </template>
        </van-cell>
        <van-cell title="参与方式" :is-link="true" value="participationMethodMapping(participationWay)" />

        <van-cell value-class="cell-file-class" title="发言稿">

          <div class="item" v-for="item in fileList" :key="item.id">
            {{ item.fileName }}
          </div>
        </van-cell>
        <van-cell value-class="cell-file-class-2" title="">
          <div style="width: 100%;text-align: left;">发言稿发言稿发言稿发言稿发s言稿发言稿发言稿发言稿</div>
        </van-cell>

      </div>

    </div>
    <div class="wrap-box">联主题评级需关联主题评联主题评-级需关联主-题评研报关联主题评级需关联主题评级需求-202222222.pdf</div>

    <div class="title">
      <span class="title__tag">7级</span>
      <span class="title__text">单元格</span>
    </div>



    <div class="border-solid width-400px">
      <span class="text-4xl">福建省</span>
      <span class="text-sm">泉州市</span>
    </div>
    <div class="bg-dark">
      <div class="background">
        1
        <!-- 这个div将显示PNG图片作为背景 -->
      </div>
    </div>


    <div class="trip-box">
      <div class="trip-box__circle-container">
        <div class="trip-box__circle-container__circle--1"></div>
        <div class="trip-box__circle-container__dashed-line"></div>
        <div class="trip-box__circle-container__circle--2"></div>
      </div>
      <div class="trip-box__time">
        <div class="trip-box__time--start"><span>开始</span> &nbsp; 2024/01/20 06:00</div>
        <div class="trip-box__time--end"><span>结束</span> &nbsp; 2024/01/20 06:00</div>

      </div>
      <div class="trip-box__transport">
        <span>火车</span>
      </div>
    </div>

    <div class="w-400px">
      <van-cell class="file-class-main" title="发言稿" value-class="cell-file-class">
        <div class="item">
          发言发言稿稿发言稿发言稿发言稿发言稿发言稿-20202.2112.pdf
        </div>
        <div class="item">
          发言发言稿稿发言稿发言稿发言稿发言稿发言稿-20202.2112.pdf
        </div>
      </van-cell>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VantCellCustomView',
  data() {
    return {
      checked: false,
      fileList: [
        {
          "bucketName": "clientapp",
          "creator": "880606",
          "fileName": "flowable接口流程 (1).docx",
          "fileOutUrl": "https://123.docx",
          "correlationSpanExtend": "",

          "moduleSpan": "singlePersonFile",
          "fileUrl": "http://222.docx",
          "correlationId": "881083",
          "id": 5020,
          "moduleId": "meet-1702459029127",
          "correlationSpan": ""
        },
        {
          "bucketName": "clientapp",
          "creator": "880606",
          "fileName": "API接口文档-20230331-3.0 (1).xlsx",
          "fileOutUrl": "https://**********:7501/data/bucketName/API接口文档-20230331-3.0 (1).xlsx",
          "correlationSpanExtend": "",

          "moduleSpan": "singlePersonFile",
          "fileUrl": "http://**********:9001/clientapp/API接口文档-20230331-3.0 (1).xlsx",
          "correlationId": "881026",
          "id": 5021,
          "moduleId": "meet-1702459029127",
          "correlationSpan": ""
        },
        {
          "bucketName": "clientapp",
          "creator": "880606",
          "fileName": "联主题评级需关联主题评联主题评级需关联主题评联主题评级需关联主题评研报关联主题评级需求-202222222.pdf",
          "fileOutUrl": "https://**********:7501/data/bucketName/API接口文档-20230331-3.0 (1).xlsx",
          "correlationSpanExtend": "",

          "moduleSpan": "singlePersonFile",
          "fileUrl": "http://**********:9001/clientapp/API接口文档-20230331-3.0 (1).xlsx",
          "correlationId": "881026",
          "id": 5021,
          "moduleId": "meet-1702459029127",
          "correlationSpan": ""
        }
      ]
    }
  },
  methods: {
    clickSwitch() {
      this.$nextTick(() => {
        console.log(`output->'11231231`, this.checked)
      })
    }
  }
};
</script>
<style scoped lang="less">
.title-value {
  flex: 3
}

.title {
  border: 1px solid red;
  display: inline-block;

  text-align: center;
  &__tag {
    display: inline-flex;

    vertical-align: middle;

    align-items: center;
    color: #071d8a;
    border: 1px solid yellow;
    height: 7px;
    background: linear-gradient(to right, #97afe6, #beccec, #97afe6);
    border-radius: 2px;
    padding: 2.5px 5px;
    font-size: 5px;
    margin-right: 5px;
    font-weight: bold;
    
  }
}

.background {

  height: 80px;
  width: 150px;
  border: 1px solid red;
  background-image: url('./1.png'), url('./2.png');
  background-position: bottom left, bottom right;
  background-size: 50% auto, auto 100%;
  background-repeat: no-repeat, no-repeat;

}

.agency-box {
  // border: 1px solid red;

  width: 400px;

  &__agency-cell,
  &__agency-cell-2 {
    .van-cell__title {
      border: 1px solid red;
      display: none;
    }

    display: flex;
    flex-direction: column;
    align-items: left;
  }

  &__agency-cell {


    &__agency-item {
      // background-color: rgba(241, 246, 255, 1);
      background-image: url('./1.png'), url('./2.png');
      background-position: left 50% bottom, bottom right;
      background-size: 50% auto, auto 100%;
      background-repeat: no-repeat, no-repeat;
      text-align: left;
      border-radius: 10px;
      padding: 10px;

      //  font-size: 12px;
      &__title {
        // display: flex;

        &--1 {
          // font-size: 22px;
          font-weight: bold;


        }

        &--2 {
          color: rgba(255, 131, 0, 1);
          // margin-left: 9px;
          font-size: 12px;
          border: 1px solid red;
          //  vertical-align: bottom;
        }
      }

      &__addr {
        font-size: 12px;
      }


    }



  }

  &__agency-cell-2 {
    &__title-box {
      display: flex;
      justify-content: space-between;
      align-items: center;

      &__icon {
        margin-left: auto;
      }
    }

    &__form {
      background-color: #f7f7f7;
      border-radius: 5px;

      .van-cell {
        background-color: transparent;
      }
    }
  }
}

.trip-box {
  border: 1px solid red;

  width: 500px;
  display: flex;

  &__circle-container {
    margin-left: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    // height: 40px;
    &__circle--1,
    &__circle--2 {

      background-color: rgba(36, 112, 234, 0.1);
      border-radius: 50%;
      width: 8px;
      height: 8px;
      border: 1px solid rgba(36, 112, 234, 1);

    }

    &__circle--2 {
      background-color: rgba(247, 101, 10, 0.1);
      border: 1px solid rgba(247, 101, 10, 1)
    }

    &__dashed-line {
      width: 0px;
      height: 20px;
      background-color: transparent;
      border-left: 3px dotted lightgray;
    }
  }

  &__time {
    margin-left: 5px;
    position: relative;

    ::after {
      content: '';
      position: absolute;
      top: 50%;
      right: -10px;
      transform: translateY(-50%);
      height: 80%;
      border-right: 2px solid lightgray;
    }

    &--start {
      margin-bottom: 4px;
    }

    border: 1px solid red;
    display: flex;
    flex-direction: column;

    justify-content: center;
  }

  &__transport {

    border: 1px solid red;
    margin-left: 20px;
  }

}



.cell-file-class {
  flex: 3;

  .item {
    overflow-x: auto;
    white-space: nowrap;

    
    text-align: left;
    border: 1px solid rgb(0, 255, 85);
  }
}

.file-class-main {
  border: 1px solid red;
  display: flex;
  flex-direction: column;

  // align-items: flex-start;

  .cell-file-class {
    border: 1px solid rgb(16, 227, 20);
    color: rgb(244, 51, 7);
    display: flex;

    .item {
      overflow-x: auto;
      /* 水平溢出时出现滚动条 */
      white-space: nowrap;
      /* 防止文本换行 */


      text-align: left;
      border: 1px solid rgb(0, 255, 85);
    }
  }


}



.wrap-box {
  width: 180px;
  word-break: break-all;
  border: 1px solid red;
}




.box-title {

  border: 1px solid red;
}

.van-cell {
  padding-top: 2px;
  padding-bottom: 2px;
  //line-height: 48px;
  font-size: 13px;

  //font-weight: bold;
  &::after {
    border-bottom: none;
  }

  /deep/ .van-field__body {}

  /deep/ .van-field__control.van-field__control--right {
    //border: 1px solid red;
    //word-break: break-all;

  }

  /deep/ .van-cell__title {
    color: #000000 !important;
  }

  .van-cell__value,
  .van-field__value {
    color: black;
  }

  /deep/ .van-field__control {
    color: black;
  }

  /deep/ .van-field__control[disabled] {
    color: #a8a8a8;
    opacity: 1;
    -webkit-text-fill-color: #000; // ios 和 安卓9.0 必须添加此属性，才会生效
    -webkit-opacity: 1;
  }

}


.container {
  border-radius: 8px;
  font-size: 14px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin-left: 20px;
  margin-right: 20px;
  margin-bottom: 10px;
  box-shadow: 0 2px 10px rgba(71, 105, 255, 0.1);
}

.person-index {
  position: relative;
  padding-top: 15px;
  padding-bottom: 15px;
  height: unset;


  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    width: calc(~"100% - 32px");
    border-bottom: 1px solid rgba(142, 152, 180, 0.2);
    transform: translateX(-50%);
  }


  &.cell {
    // border: 1px solid red;
    background-color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 15px;
    height: 50px;

    .title {
      font-weight: bold;
    }

    span {

      color: #333;
    }

    .select {
      color: darkgray;
      font-weight: unset;
    }
  }
}

.header {
  //border:1px solid red;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 8px 8px 0 0;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    bottom: 3px;
    left: 50%;
    width: calc(~"100% - 32px");
    border-bottom: 1px solid rgba(142, 152, 180, 0.2);
    transform: translateX(-50%);
  }

  // margin-bottom: -11px;
  .left {
    //border: 1px solid red;
    margin: 12px 0;
    padding: 0px 16px;
    //border-left: 3px solid #3c6cfe;
    text-align: left;
    display: flex;
    align-items: center;
    font-size: 15px;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      bottom: 0;
      top: 2.5px;

      @media (min-width: 600px) {

        top: 1.2px;
      }

      //transform: translateY(-50%);
      height: 16px;
      /* 垂直边框的高度设置为与父元素高度相同 */
      width: 3px;
      /* 调整垂直边框的宽度 */
      background-color: #3c6cfe;
      /* 设置垂直边框的颜色 */
    }

    .h3 {
      //font-weight: normal;
      //line-height: 21px;
      font-weight: 500;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      // overflow: hidden;
      word-wrap: break-word;
      word-break: break-word;
      //word-break: break-all;
    }
  }

  .right {
    margin-left: auto;
    margin-right: 10px;

    .trash {
      height: 20px;
      width: 20px;
    }
  }


}

.header {
  &.none-bottom {


    &::after {
      content: "";
      position: absolute;
      bottom: 3px;
      left: 50%;
      width: calc(~"100% - 32px");
      border-bottom: 1px solid red;
      transform: translateX(-50%);
    }
  }
}

.switch-box {
  border: 1px solid red;
  display: flex;
  align-items: center;
}

.van-switch {
  border: 1px solid red;
  font-size: 17px !important;
}



.cell-file-class-2 {
  border: 1px solid rgb(16, 227, 20);
  color: blueviolet;
  flex: 100;
  justify-content: start;

  &.van-cell {
    padding-top: 0px;
    padding-bottom: 0px;
  }
}</style>


