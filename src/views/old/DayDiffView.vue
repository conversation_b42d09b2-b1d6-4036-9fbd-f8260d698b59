
<template>
  <div>
    <h1>DayDiffView</h1>
    {{ value1 }}
    <div>{{ dayDiff }}</div>
    <div>{{ dayDiff2 }}</div>
    <div>{{ dayDiff3 }}</div>
    <div></div>
       <el-date-picker
        :default-time="['00:00:00', '23:59:00']"
        format="yyyy-MM-dd HH:mm"
        v-model="value1"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期">
      </el-date-picker>
  </div>
</template>

<script>
export default {
  name: 'DayDiffView',
  data() {
    return {
      value1:''
    }
  },
  computed: {
    dayDiff() {
      if(!this.value1) return 0
      const date1 = this.value1[0];
      const date2 = this.value1[1]
      const differenceInMilliseconds = date2 - date1;

      // 将时间差（毫秒）转换为天数
      let  differenceInDays = differenceInMilliseconds / (1000 * 60 * 60 * 24);
      // 四舍五入到最接近的0.5
      differenceInDays = Math.round(differenceInDays * 2) / 2;
      return differenceInDays
    },
    dayDiff2() {
      if (!this.value1) return 0
     const date1 = this.value1[0];
      const date2 = this.value1[1];
      const differenceInMilliseconds = date2 - date1;

      let differenceInDays = differenceInMilliseconds / (1000 * 60 * 60 * 24);

      // 计算完整的天数
      const fullDays = Math.floor(differenceInDays);

      // 计算剩余的部分
      const remainder = differenceInDays - fullDays;

      // 如果剩余部分小于0.5天，则增加0.5天，否则增加一天
      const additionalDay = remainder <= 0.5 ? 0.5 : (remainder > 0 ? 1 : 0);

      // 最终的天数计算
      differenceInDays = fullDays + additionalDay;
      return differenceInDays
    },
    dayDiff3() {
      if (!this.value1) return 0
      const date1 = this.value1[0];
      const date2 = this.value1[1]
      const differenceInMilliseconds = date2 - date1;

      // 将时间差（毫秒）转换为天数
      let differenceInDays = differenceInMilliseconds / (1000 * 60 * 60 * 24);

      return differenceInDays
    }
  }
};
</script>
<style scoped>
</style>
