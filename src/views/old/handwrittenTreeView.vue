<!--手写属性组件-->
<template>
  <div>
    <AgencyPicker :show="show" type="visit" />
  </div>
</template>

<script>
import AgencyPicker from "@/components/AgencyPicker.vue";
export default {
  name: 'handwrittenTreeView',
  components: {
    AgencyPicker,
  },
  data() {
    return {
      show: true,
    };
  },
  methods: {},
  mounted() {},
};
</script>

<style scoped lang="scss">
.box {
  //border: 1px solid red;
  height: calc(100vh - 100px);
  overflow: scroll;
}
</style>
