<!--elem选择器本地分页,并实现滚动加载-->
<template>
  <div>
    {{input}}
    selected {{selected}}
    <el-input v-model="input"></el-input>
    <el-select v-model="selected"
               filterable
               remote
               :remote-method="remoteMethod"
                @blur="blur"
               @focus="focus"
               clearable
               v-selectloadmore="loadMore">
      <el-option
          v-for="option in options"
          :label="option.label"
          :value="option.value"
          :key="option.value"
      ></el-option>
    </el-select>


  </div>
</template>

<script>
import directives from '@/directives/directives.js'

export default {
  name: 'selectLoadMoreView',
  data() {
    return {
      input:'',
      finish:false,
      loading: true,
      selected: "",
      options: [],
      currentPage: 1,
      pageSize: 30,
      origOptions:[],
      tempOptions:[]
    }
  },
  computed: {
    pageSlice(){
      return this.origOptions.slice((this.currentPage-1)*this.pageSize,this.currentPage*this.pageSize)
    }
  },
  created() {
    for (let i = 1; i <= 100; i++) {
      this.origOptions.push({
        value: `选项${i}`,
        label: `黄金糕${i}`
      });
    }
    this.loadMore()

  },
  watch:{
    input(val){
      this.selected=val
      this.options=this.origOptions.filter(item=>{
        return item.value===val
      })
    }
  },
  methods: {
    blur(){
      // this.options = this.tempOptions
      // this.finish=false
    },
    focus(){
      console.log(this.selected)
      if(this.selected===''){
        this.options = this.tempOptions
        this.finish=false

      }
    },
    remoteMethod(query){
      if (query !== '') {
        this.finish = true;
        this.options = this.origOptions.filter(item => {
          return item.label.toLowerCase()
              .indexOf(query.toLowerCase()) > -1;
        });
      }
    },
    loadMore() {
      if (!this.finish&&this.currentPage <= Math.ceil(this.origOptions.length / this.pageSize)) {
        this.options = [...this.options, ...this.pageSlice];
        this.tempOptions = this.options
        this.currentPage++;
        console.log('loading')
      } else {
        // 所有数据已加载，禁用加载更多
        this.loading = false;
      }
    }
  }
};
</script>
