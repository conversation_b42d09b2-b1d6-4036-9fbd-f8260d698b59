
<template>
  <div>
    <h1>OverflowDirectiveView</h1>


    <span v-if="firstList.length > 0" v-line-count="'f'" class="ellipsis-two-lines">
     
      <span class="name" v-for="item in sliceName(firstList, 'f')">{{ item }}</span>
      <span v-show="firstList.length > nameCount['f']">...等{{ firstList.length }}人</span>

    </span>

    <span v-if="secondList.length > 0" v-line-count="'s'" class="ellipsis-two-lines">
      <span class="name" v-for="item in sliceName(secondList, 's')">{{ item }}</span>
      <span v-show="secondList.length > nameCount['s']">...等{{ secondList.length }}人</span>


    </span>
    <button @click="addName">按钮</button>
   
  </div>
</template>

<script>
import directives from '@/directives/directives.js'

export default {
  name: 'OverflowDirectiveView',
  data() {
    return {
      nameList: [],
      nameCount: {
        f: 0,
        s: 0
      },

    }
  },
  mounted() {
    this.$on('directive-result', (result, value) => {
      console.log('每行显示数量:', result, value);
      this.nameCount[value] = result
    });
  },
  computed: {
    firstList() {
      return this.nameList.slice(0, 10)
    },
    secondList() {
      return this.nameList.slice(0,8);
    }
  },
  methods: {

    addName() {
      this.nameList = [
        "John",
        "Mary",
        "David",
        "Sarah",
        "Michael",
        "Emily",
        "James",
        "Emma",
        "Andrew",
        "Olivia",
        "Daniel",
        "Sophia",
        "Matthew",
        "Ava",
        "Christopher",
        "Isabella",
        "William",
        "Grace",
        "Alexander",
        "Chloe"
      ];
    },
    sliceName(list, value) {
      const count = this.nameCount[value];
      if (count === 0 || list.length <= count) {
        return list;
      }
      return list.slice(0, count);
    }
  },

};
</script>
<style scoped lang="scss">
// SCSS样式
.ellipsis-two-lines {
  border: 1px solid red;
  width: 200px;
  display: flex;
  flex-wrap: wrap;
}

.name {
  border: 1px solid red;
  margin-right: 2px;
}

.cell-custom {
cursor:default;
  border: 1px solid red;
}
</style>
