<!--避免ios橡皮筋效果影响滚动-->
<template>
  <div style="border: 1px solid red;height: 100vh;overscroll-behavior: contain;">
    <van-tabs >
      <van-tab  v-for="index in 8" :title="'标签 ' + index">
        <div  style="border: 1px solid red;height: 100px;overflow:scroll;">
          <div  style="border: 1px solid red;height: 300px"> 内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容 {{ index }}
          </div>
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
export default {
  name: 'disableiosRubberBandEffectDemo',
  data() {
    return {}
  },
  computed: {},
  created() {

  },
  mounted() {

  },
  methods: {}
};
</script>
<style>

</style>
