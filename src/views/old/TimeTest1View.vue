<template>
  <div>

    <div class="block">
      <div>{{ value3 }}</div>
      <span class="demonstration">设置默认时间</span>
      <el-date-picker v-model="value3" type="datetime" :format="formatTime()" placeholder="选择日期时间">
      </el-date-picker>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TimeTest1View',
  data() {
    return {
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            picker.$emit('pick', new Date());
          }
        }, {
          text: '昨天',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24);
            picker.$emit('pick', date);
          }
        }, {
          text: '一周前',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', date);
          }
        }]
      },
   
      value3: ''
    };
  },
  methods: {
    formatTime() {
   
      if(this.value3){
        const timePick = this.value3
        const hours = timePick.getHours();
        const minutes = timePick.getMinutes();
        const seconds = timePick.getSeconds();
        if (hours === 0 && minutes === 0 && seconds === 0) {
       
          console.log('true');
          return 'yyyy'
        } else {
          console.log('false');
          return ''
        }

       
      } else {
        return ''
      }
        


    }
  },
};
</script>
<style scoped>
</style>
