
<template>
  <div>
    <h1>ElMessageView</h1>
    <el-button :showConfirmButton="true"  @click="openHTML">使用 HTML 片段</el-button>
      <div class="loading-block" v-loading="true" element-loading-spinner="el-icon-loading"></div>

  </div>
</template>

<script>
export default {
  name: 'ElMessageView',
  methods: {
    openHTML() {
      this.$message({

         message: '请补充您的参与信息',
        type: 'warning'

      });
    },
    hello(){
      console.log(`output->'111111111`)
    }
  }
};
</script>
<style scoped lang="scss">
.loading-block {
  position: relative;
  // border: 1px solid red;
  height: 100px;
  width: 50px;
  ::v-deep .el-loading-mask{
  border: 1px solid red!important;
  display: flex!important;
  align-items: center!important;
  .el-loading-spinner{
    top:unset;
    margin-top: 0;
  }
}
}

</style>
