
<template>
  <div>
    <h1>ElemTableValidateView</h1>
    <el-form :model="dynamicValidateForm" ref="dynamicValidateForm" class="demo-dynamic">
      <el-table :data="dynamicValidateForm.tableData" style="width: 100%">
        <el-table-column prop="date" label="日期" width="180">
        </el-table-column>
        <el-table-column prop="name" label="姓名" width="180">
        </el-table-column>
        <el-table-column prop="name" label="姓名" width="180">
          <template slot-scope="scope">
            <el-form-item :prop="`tableData.${scope.$index}.file`" :rules="[
              { required: true, message: '请输入', trigger: 'blur' },

            ]">

              <el-button type=""></el-button>
              <el-input>
                <div class="" style="height: 30px;width: 200px">{{ scope.row.file }}</div>
              </el-input>
            </el-form-item>
          </template>
        </el-table-column>

      </el-table>



      <el-form-item>
        <el-button type="primary" @click="submitForm('dynamicValidateForm')">提交</el-button>

        <el-button @click="resetForm('dynamicValidateForm')">重置</el-button>
      </el-form-item>
    </el-form>


  </div>
</template>

<script>
export default {
  name: 'ElemTableValidateView',
  data() {
    return {
      dynamicValidateForm: {
        tableData: [{
          date: '2016-05-02',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1518 弄',
          file: ''
        }],

      },

    };
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid, object) => {
        if (valid) {
          alert('submit!');
        } else {
          console.log('error submit!!', object);
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    removeDomain(item) {
      var index = this.dynamicValidateForm.domains.indexOf(item)
      if (index !== -1) {
        this.dynamicValidateForm.domains.splice(index, 1)
      }
    },
    addDomain() {
      this.dynamicValidateForm.domains.push({
        value: '',
        key: Date.now()
      });
    }
  }
};
</script>
<style scoped></style>
