
<template>
  <div class="wrapper">
    <h1>VanPopupInVancellView</h1>
    <van-swipe-cell :stop-propagation="true" :before-close="beforeClose" @open="openSwipe" @close="closeSwipe"
      style="border:1px solid red">
      <div :style="pointerEvents" class="pl-55 w-90 h-20 border-solid">
        <button @click="open">打开</button>
        <button @click="test">样式</button>
        <button @click="test2">移除</button>
      </div>
      <template #right>
        <van-button square type="danger" text="删除" />
        <van-button square type="primary" text="收藏" />
      </template>


      <van-popup v-model="show">内容</van-popup>
    </van-swipe-cell>
  </div>
</template>

<script>

export default {
  name: 'VanPopupInVancellView',
  data() {
    return {
      pointerEvents: '',
      show: false
    }
  },
  methods: {
    beforeClose() {
      console.log('before')
    },
    open() {
     
    
      this.show = true


    },
    closeSwipe() {
          this.pointerEvents = ''
      this.test()
  
    },
   
    openSwipe() {
      this.pointerEvents = 'pointer-events:none'
      this.test2()

    },
    test() {
      // 获取根元素
      const rootElement = document.documentElement;

      // 创建一个样式元素
      const styleElement = document.createElement('style');
      // 设置样式内容
      styleElement.innerHTML = `
        .van-swipe-cell .van-swipe-cell__wrapper {
          transform: unset !important;
        }
      `;

      // 将样式元素添加到根元素下
      rootElement.appendChild(styleElement);


    },


    test2() {
      // 通过选择器查找添加到根元素下的所有 <style> 元素
      const styleElements = document.querySelectorAll('style');

      // 创建一个函数用于移除样式
      function removeSpecificStyle(selector) {
        // 遍历所有选中的样式元素
        styleElements.forEach((styleElement) => {
          // 获取样式元素的 innerHTML
          const elementInnerHTML = styleElement.innerHTML;

          // 检查 innerHTML 是否包含我们的选择器
          if (elementInnerHTML.includes(selector)) {
            // 从根元素中移除这个样式元素
            styleElement.parentNode.removeChild(styleElement);
          }
        });
      }

      // 调用函数移除特定的样式
      removeSpecificStyle('.van-swipe-cell .van-swipe-cell__wrapper');

    }
  }
};
</script>
<style  lang="scss">
::v-deep .van-swipe-cell .van-swipe-cell__wrapper {
  transform: unset !important;
}
</style>
