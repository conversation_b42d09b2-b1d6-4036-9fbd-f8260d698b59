<template>
  <div class="form-container">
    {{ form }}
    <el-form ref="form" :model="form" label-width="32px">
      <el-form-item label="状态">
        <el-select v-model="form.status" placeholder="请选择状态">
          <el-option label="正常" value="normal"></el-option>
          <el-option label="异常" value="abnormal"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="部门">
        <el-select collapse-tags v-model="form.department" multiple placeholder="请选择部门">
          <el-option label="部门部门部门A" value="A"></el-option>
          <el-option label="部门B" value="B"></el-option>
          <el-option label="部门C" value="C"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="研究员" class="form-item-label" label-width="52px">
        <el-select collapse-tags v-model="form.researcher" multiple placeholder="请选择研究员">
          <el-option label="研究员A" value="A"></el-option>
          <el-option label="研究员B" value="B"></el-option>
          <el-option label="研究员C" value="C"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="日期">
        <el-date-picker style="width: 220px;" type="datetimerange" v-model="form.dateRange" start-placeholder="开始日期"
          end-placeholder="结束日期" range-separator="至" format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
      </el-form-item>

      <el-form-item label="名称">
        <el-input suffix-icon="el-icon-search" v-model="form.search" placeholder="请输入搜索内容"></el-input>
      </el-form-item>

      <el-form-item>

      </el-form-item>
    </el-form>
    <el-button type="primary" @click="handleSearch">查询</el-button>
    <el-button @click="handleReset">重置</el-button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form: {
        status: '',
        department: ["A", "B"],
        researcher: [],
        dateRange: [],
        search: ''
      }
    };
  },
  methods: {
    handleSearch() {
      // 处理查询逻辑
    },
    handleReset() {
      this.$refs['form'].resetFields();
    }
  }
};
</script>

<style lang="scss" scoped>
.form-container {
  ::v-deep .el-form {
    display: flex;

    .el-form-item {
      margin-right: 5px;

      .el-form-item__label {

        padding: 0;
        
      }
      .el-select__tags{
        border: 1px solid yellowgreen;
        display: flex;
        // flex-wrap: nowrap;
        .el-tag {
            border: 1px solid red;
        
            .el-select__tags-text {
              // min-width: 19px;
              // max-width: 80px;
            }
          }
      }
     

      .el-input {
        margin-right: -20px;
        .el-input__inner {
         
          width: 90%;
        }

        .el-input__suffix {
        
          right: 15px
        }
      }


    }

  }

  border: 1px solid red;

  max-width: 1200px;
  margin: 0 auto;

  .el-form-item {
    margin-bottom: 20px;
  }
}
</style>
