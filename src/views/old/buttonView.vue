<template>
 <div ref="boxref">
1212
<button @click="test">1</button>
 </div>
</template>

<script>
import { Dialog } from 'vant';

export default {
  data() {
    return {
   

    };
  },
  methods: {
    test() {
      const el = this.$refs.boxref
      console.log(`output->tht`, el.offsetHeight)

  }
  },
  mounted() {
  }
}
</script>

<style lang="scss">
.box{
  display: flex;
  flex-direction: column;
  border: 1px solid red;
  height: 230px;
  width: 400px;
  overflow: scroll;
}

</style>
