<template>
  <div>
    <!-- 检索条件 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="姓名">
        <el-input v-model="searchForm.name"></el-input>
      </el-form-item>
      <el-form-item label="所属团队">
        <el-select v-model="searchForm.team" placeholder="请选择">
          <el-option label="团队A" value="A"></el-option>
          <el-option label="团队B" value="B"></el-option>
          <el-option label="团队C" value="C"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格 -->
    <el-table :data="tableData" style="width: 100%" border>
      <el-table-column label="姓名" prop="name"></el-table-column>
      <el-table-column label="OA号" prop="oa"></el-table-column>
      <el-table-column label="所属团队" prop="team"></el-table-column>
      <el-table-column label="状态" prop="status"></el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchForm: {
        name: '',
        team: ''
      },
      tableData: [
        { name: '张三', oa: '1001', team: 'A', status: '正常' },
        { name: '李四', oa: '1002', team: 'B', status: '离职' },
        { name: '王五', oa: '1003', team: 'C', status: '在职' }
      ]
    };
  },
  methods: {
    handleSearch() {
      // 处理搜索逻辑
      console.log('搜索条件:', this.searchForm);
    },
    handleReset() {
      // 重置搜索条件
      this.searchForm.name = '';
      this.searchForm.team = '';
    }
  }
};
</script>

<style lang="scss">
.search-form {
  margin-bottom: 20px;

  .el-form-item {
    margin-right: 20px;
  }
}
</style>
