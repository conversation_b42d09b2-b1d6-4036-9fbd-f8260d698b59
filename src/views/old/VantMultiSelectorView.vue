<template>
  <div>
    <h1>VantMultiSelectorView</h1>
    <button @click="show = true">show</button>
    <van-cell title-style="border: 1px solid red;margin-right:-20px" clickable title="单元格" value="内容内容内容内容内容内容" is-link/>

    <van-popup
        v-model="show"
        position="bottom"
        :style="{ height: '55%' }"
    >

      <van-cell-group>

        <van-cell
            v-for="(item, index) in clockOptions"
            :key="item.id"
            clickable
            @click="changeClock(item)"
        >
          <template #title>
            <div class="person-box" style="">
              <span class="title" style=""> {{ item.label }}</span>
            </div>
          </template>
          <template #right-icon>
            <div v-show="clock.includes(item.id)" class="checkmark">
              <svg viewBox="0 0 16 16">
                <path fill="none" stroke="#3C6CFE" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      d="M2,8 L6,12 L14,4"/>
              </svg>
            </div>
          </template>
        </van-cell>

      </van-cell-group>
      {{ clock }}
      {{getClockOptions}}
    </van-popup>


  </div>
</template>

<script>
import cityData from "@/data/province&city";

export default {
  name: 'VantMultiSelectorView',
  data() {
    return {
      show: false,
      clock: ['0'],
      clockOptions: [
        {id: '0', label: '无提醒'},
        {id: '1', label: '提前15分钟'},
        {id: '2', label: '提前30分钟'},
        {id: '3', label: '提前1小时'},
        {id: '4', label: '提前1天'}
      ]
    }
  },
  mounted() {

  },
  methods: {
    changeClock(item) {
      console.log(item)

      function updateArray(arr, element) {
        // 检查元素是否为'0'
        if (element === '0') {
          // 如果是'0'，清空数组并只添加'0'
          return ['0'];
        } else {
          // 如果元素不是'0'，先移除数组中的'0'（如果有的话）
          const zeroIndex = arr.indexOf('0');
          if (zeroIndex > -1) {
            arr.splice(zeroIndex, 1);
          }

          // 查找元素在数组中的位置
          const index = arr.indexOf(element);
          if (index > -1) {
            // 如果元素已存在，就删除它
            arr.splice(index, 1);
          } else {
            // 如果元素不存在，就添加它
            arr.push(element);
          }
          return arr;
        }
      }

      this.clock = updateArray(this.clock, item.id)
    }
  },
  watch: {},
  computed: {
    getClockOptions() {
      const clock = this.clock
      const clockOptions = this.clockOptions;
      const selectedOptions = clock.map(clockId => {
        const option = clockOptions.find(opt => opt.id === clockId);
        return option ? option.label : '';
      });
      const labelString = selectedOptions.join(',');
      return (clock[0]==='0'?'':'提前')+labelString.replace(/提前/g, '');
      //'提前15分钟,提前30分钟,提前1小时'.replace(/(提前[^,]+),/g, '$1');
    }
  }
};
</script>
<style scoped lang="scss">
.checkmark {

  padding-right: 10px;

  svg {
    width: 15px;
    margin-right: 26px;
  }


  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  //background: linear-gradient(to right, transparent, rgba(229, 227, 227, 0.82));
  padding-left: 10px;

  display: flex;
  align-items: center; /* To align the SVG icon vertically */
}
</style>
