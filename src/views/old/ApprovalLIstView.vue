<template>
  <div>
    <h1>ApprovalLIstView</h1>
    <div class="box">

      <!-- <div class="processYuan">

        <div class="circular"></div>
        <div class="triangle"></div>
        <div class="arrowhead"></div>
      </div> -->

      <div class="processList" v-for="(item, index) in approvalData" :key="index" v-show="index === 0 || isShowHq">

        <div class="processYuan">
          <div class="circular" v-if="index === 0"></div>
          <div class="triangle" v-if="index === 0"></div>
          <div class="arrowhead"></div>
        </div>


        <div class="process-right">
          <div class="top">
            <div class="processSpan">
              <span>{{ item.taskName }}</span>22
              <span class="remarks"></span>
              <span class="remarks" :class="[item.type == '已跳过' || item.type == '已撤回' ? 'colorCustom' : '']">{{
        item.type
      }}</span>
              <!-- <span class="remarks" v-if="item.message">意见：{{ item.message }}</span> -->
            </div>
            <div class="processSpan">
              <span>{{ item.waitDoneUsername }}</span>
            </div>
            <div class="processTime">
              <span>{{ item.endTime }}</span>
              <span @click="clickIsShowHq" :class="isShowHq ? 'arrowRotate' : 'arrow'"></span>
            </div>
          </div>
          <span class="remarks"
            v-if="item.message">意见：asasdfasdf赛风意见：asasdfasdf赛风意见：asasdfasdf赛意见：asasdfasdf赛风意见：asasdfasdf赛风意见：asasdfasdf赛风风意见：asasdfasdf赛风{{
        item.message
      }}</span>

        </div>
      </div>

    </div>
  </div>
</template>

<script>
export default {
  name: 'ApprovalLIstView',
  data() {
    return {
      isShowHq: true,
      "tableData": [
        {
          "nodeName": "Node 1",
          "state": "0",
          "serviceSubmitName": "Service Name 1",
          "remarks": "Sample remark for node 1"
        },
        {
          "nodeName": "Node 2",
          "state": "1",
          "serviceSubmitName": "Service Name 2",
          "remarks": "Sample remark for node 2"
        },
        {
          "nodeName": "Node 3",
          "state": "2",
          "serviceSubmitName": "Service Name 3",
          "remarks": "Sample remark for node 3"
        }
      ],
      "approvalData": [
        {
          "taskDefKey": "sit-canyurenhuiqian-task",
          "duration": "102837",
          "startTime": "2024-01-04 16:22:54",
          "taskName": "参与人会签",
          "endTime": "2024-01-04 16:24:36",
          "message": "同意",
          "type": "已审核",
          "waitDoneUserId": "881026",
          "taskId": "753676a8-aada-11ee-a349-0242e8372d9a",
          "waitDoneUsername": "张琦"
        },

        {
          "taskDefKey": "sit-canyurenhuiqian-task",
          "startTime": "2024-01-04 16:22:54",
          "taskName": "参与人会签",
          "waitDoneUserId": "880202",
          "taskId": "75364f89-aada-11ee-a349-0242e8372d9a",
          "waitDoneUsername": "郭嘉沂"
        },
        {
          "taskDefKey": "sit-canyurenhuiqian-task",
          "startTime": "2024-01-04 16:22:54",
          "taskName": "参与人会签",
          "waitDoneUserId": "828939",
          "taskId": "75364f84-aada-11ee-a349-0242e8372d9a",
          "waitDoneUsername": "李雨桐"
        },
        {
          "taskDefKey": "sid-faqiren-task",
          "duration": "53",
          "startTime": "2024-01-04 16:22:54",
          "taskName": "发起人",
          "endTime": "2024-01-04 16:22:54",
          "message": "",
          "type": "已提交",
          "waitDoneUserId": "880606",
          "taskId": "752d27a6-aada-11ee-a349-0242e8372d9a",
          "waitDoneUsername": "李璐琳"
        }
      ]
    }
  },
  methods: {
    clickIsShowHq() {
      this.isShowHq = !this.isShowHq;

    },
  }
};
</script>
<style scoped lang="less">
.box {
  height: 300px;
  border: 1px solid red;
  position: relative;

  width: 600px;

  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #1d2341;
  font-weight: 400;

  .processList {
    // 
    overflow: hidden;
    margin-bottom: 30px;
    border: 1px solid rgb(240, 246, 45);
    display: flex;
    align-items: stretch;

    .processYuan {
      float: left;
      width: 8%;
      border: 0 solid rgb(12, 247, 55);

      z-index: 99;

      .circular {
        // background: #67C23A;
        background: #67C23A;
        border-radius: 50%;
        height: 10px;
        width: 10px;
        margin-left: 0.2rem;
        margin-top: 3px;
      }

      .rhombus {
        clip-path: polygon(50% 0, 100% 50%, 50% 100%, 0 50%);
        transition: 1s clip-path;
        height: 10px;
        width: 10px;
        margin-left: 0.2rem;
        margin-top: 3px;
        background: #dcef0b;
      }

      .arrowhead {
        width: 1px;
        height: 100%;
        background: #3c6cfe;
        margin-left: 12px;
      }

      .triangle {
        z-index: 99;
        width: 0px;
        height: 0px;
        border: 5px solid transparent;
        border-bottom-color: #3c6cfe;
        margin-left: 7.5px;
      }

      .wuColor {
        background: #c6cbd9;
      }

      .triangleColor {
        border-bottom-color: #c6cbd9;
      }


    }

    .process-right {
      flex: 1;
      display: flex;
      flex-direction: column;
      border: 2px solid pink;

      .processSpan {
        border: 1px solid pink;
        padding: 5px 0 0 0;
        width: 24.5%;
        min-height: 10px;
        float: left;

        span {
          display: block;
        }


      }

      .processTime {
        width: 42%;
     
        border: 1px solid pink;
        // margin-left: auto;
        float: right;
        
        position: relative;
        padding: 5px 0;

        .arrow::after {
          content: "";
          position: absolute;
          top: 4px;
          right: 8px;
          width: 6px;
          height: 6px;
          border-right: 1px solid #000;
          border-bottom: 1px solid #000;
          transform: rotate(45deg);
        }

        .arrowRotate::after {
          content: "";
          position: absolute;
          top: 8px;
          right: 8px;
          width: 6px;
          height: 6px;
          border-right: 1px solid #000;
          border-bottom: 1px solid #000;
          transform: rotate(225deg);
        }

     
      }

      .remarks {
        font-family: PingFangSC-Regular;
        font-size: 11px;
        color: #8e98b4;
        font-weight: 400;
        margin-top: 5px;
      }
    }



  }

  .processList {
    margin-bottom: 0;
    overflow: hidden;
  }


}
</style>
