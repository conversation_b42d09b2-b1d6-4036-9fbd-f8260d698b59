<template>
  <div>
    <el-cascader :show-all-levels="false" :append-to-body="false" :popper-class="cascaderCustomClass"
      @expand-change="handleExpandChange" v-model="selectedMeetingTypes" :options="meetingTypeOptions"
      placeholder="请选择会议类型" @change="handleMeetingTypeChange">
    </el-cascader>
    <p>选中的会议类型selectedMeetingType：{{ selectedMeetingType }}</p>
    <p>选中的会议类型selectedMeetingTypes：{{ selectedMeetingTypes }}</p>
  </div>
</template>

<script>
export default {
  name: 'SecondaryMeetingMenuView',
  data() {
    return {
      hintVisible: false,
      meetingTypeOptions: [
        {
          label: '公司内部会议',
          value: 'company',
          children: [
            { label: '日常会议', value: '18' },
            { label: '月例会', value: '15' },
            { label: '党委会', value: '16' },
            { label: '培训', value: '17' },
          ],
        },
        {
          label: '集团会议',
          value: 'group',
          children: [
            { label: '风险会', value: '1' },
            { label: '资负会', value: '2' },
            { label: '财富会', value: '3' },
            { label: '策略会', value: '4' },
            { label: '电话会议', value: '5' },
            { label: '行内例会', value: '6' },
            { label: '常规会议', value: '7' },
          ],
        },
        {
          label: '其他',
          value: 'other',
          children: [
            { label: '论坛', value: '8' },
            { label: '讲坛', value: '9' },
            { label: '讲座', value: '10' },
            { label: '年会', value: '11' },
            { label: '报告会', value: '12' },
            { label: '研讨会', value: '13' },
            { label: '媒体活动', value: '14' },
          ],
        },
      ],
      selectedMeetingTypes: [],
      selectedMeetingType: '',
    };
  },
  watch: {
    selectedMeetingType(value) {
      function findParentLabel(options, value) {
        for (let i = 0; i < options.length; i++) {
          const parent = options[i];
          const children = parent.children;
          for (let j = 0; j < children.length; j++) {
            if (children[j].value === value) {
              return [parent.value, value];
            }
          }
        }
      }
      
      console.log(`output->value`,value)
      console.log(`output->value`, findParentLabel(this.meetingTypeOptions, value))
    }
  },
  computed: {
    cascaderCustomClass() {
      return this.hintVisible ? 'cascaderCustom' : ''
     
    }
  },
  methods: {
    handleMeetingTypeChange(value) {
      console.log(`output->变化的只`, value)
      this.selectedMeetingType = value[value.length - 1];
    },
    handleExpandChange(value) {
      this.hintVisible = value[0] === 'company';
    },
  },
};
</script>
<style lang="scss" scoped >


::v-deep .cascaderCustom{
.el-cascader-menu {

    // border: 1px solid red;
    &:nth-child(2) {
      position: relative;

      &::after {
        line-height: 12px;
        font-size: 10px;
        padding: 4px;
        content: "此类会议为公司内部会议，无须审核，不会生成服务记录";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 95%;
        text-align: left;
        color: #e31f00;
      }

    }
  }
}

</style>