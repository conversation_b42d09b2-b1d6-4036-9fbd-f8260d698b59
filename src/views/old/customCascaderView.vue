<!--elem级联选择器点击名称时同时选中圆点,解决checkStrictly:true同时动态加载时出现暂无数据-->
<template>
  <div>
    {{value}}
    <el-cascader  ref="myCascader" popper-class="popper" :before-filter="beforeFilter"
      v-model="value" :options="options" :props="props" @change="handleChange" placeholder="请选择" clearable filterable>
      <template #default="props">
        <span @click="click">{{ props.node.label }}</span>
      </template>
    </el-cascader>
    <el-button @click="change">change</el-button>
  </div>
</template>

<script>
import { Cascader } from "element-ui";
function debounce(func, wait) {
  let timeout;

  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };

    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

export default {
  name: 'customCascaderView',
  components: {
    [Cascader.name]: Cascader,
  },
  data() {
    return {
      value:[],
      options: [
        {
          children: [
            // ... 你的模拟数据 ...
          ],
          orgId: "cust-1681696048",
          orgName: "王者荣耀",
          parentId: "-1",
        },
        {
          children:[],
          orgId: "cust-1681975543",
          orgName: "博彦科技1",
          parentId: "-1",
        },
      ],
      props: {
        checkStrictly: true,
        value: "orgId",
        label: "orgName",
        children: "children",
        lazy: true,
        lazyLoad: null, // 这里将在 created 钩子中初始化
      },
    };
  },
  created() {
    // 确保正确的 `this` 上下文
    const lazy = (node, resolve) => {
      const {level}=node

      console.log('12s',node);
      this.value=node.path
      const nodes=[
        {
          children: [
            {
              children: [],
              orgId: "cust-1681801039",
              orgName: "木兰织的布",
              parentId: "cust-1681800652",
              parentName: "花木兰",
            },
            {
              children: [
                {
                  children: [
                    {
                      children: [],
                      orgId: "cust-1682055503",
                      orgName: "织布机",
                      parentId: "cust-1681802439",
                      parentName: "木兰当布织",
                    },
                  ],
                  orgId: "cust-1681802439",
                  orgName: "木兰当布织",
                  parentId: "cust-1681800698",
                  parentName: "唧唧复唧唧11",
                },
              ],
              orgId: "cust-1681800698",
              orgName: "唧唧复唧唧11",
              parentId: "cust-1681800652",
              parentName: "花木兰",
            },
          ],
          orgId: "cust-1681800652",
          orgName: "花木兰",
          parentId: "cust-1681800475",
          parentName: "上单",
        },
      ]
      resolve(nodes);
    };
    this.props.lazyLoad = lazy
  },
  methods: {
    casChange(value){
      console.log(`output->value`,value)
    },
    change(){
      this.value='helo'
    },
    click(event){

      this.handleChange()
    },

    beforeFilter(inputValue){

      this.options=[{
        children: [],
        orgId: "cust-1681696048",
        orgName: "王者荣耀2",
        parentId: "-1",
      },]
      return false
    },

    handleChange(value) {
      console.log(`output->handleChange`, value)
      // const node = this.$refs.myCascader.getCheckedNodes()[0];
      if (value) {
        this.value = value[value.length - 1]
      }
      // 解决：el-cascader当设置了checkStrictly:true属性时，可以选择任意一级的菜单。但是同时设置动态加载的时候。点击前面的radio按钮会出现一个暂无数据的面板
      const panelRefs = this.$refs.myCascader.$refs.panel
      if (panelRefs.activePath.length !== 0) {
        panelRefs.activePath.forEach(item => {
          if (item.children.length === 0) {
            panelRefs.lazyLoad(panelRefs.getCheckedNodes()[0])
          }
        })
      }
    },
  },
};
</script>
<style scoped>

.popper .el-cascader-panel .el-radio {
//border: 1px solid red;
  width: 100%;
  height: 100%;
  z-index: 10;
  position: absolute;
  top: 0px;
  right: 0px;
}
.popper .el-cascader-panel .el-checkbox {
//border: 1px solid #01fd34;
  width: 100%;
  height: 100%;
  z-index: 10;
  position: absolute;
  top: 0px;
  right: 0px;
}
.popper .el-cascader-panel .el-radio__input {
//border: 1px solid #01fd34;
  margin-top: 10px;
  margin-left: 8px;
}
.popper .el-cascader-panel .el-checkbox__input {
//border: 1px solid #01fd34;
  margin-top: 2px;
  margin-left: 8px;
}
.popper .el-cascader-panel .el-cascader-node__postfix {
//border: 1px solid red;
  top: 10px;
}
.popper .el-cascader-panel .el-radio {
//pointer-events: none;

}

</style>
