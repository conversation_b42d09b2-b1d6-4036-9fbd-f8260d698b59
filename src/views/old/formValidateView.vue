
<template>
  <div>
    <van-form ref="fom" @submit="onSubmit" @failed="onFailed">
      <van-field v-model="username" name="用户名" label="用户名" placeholder="用户名"
        :rules="[{ required: true, message: '请填写用户名' }]" />

    </van-form>
    <div> </div>
    <van-button @click="test">提交呢</van-button>
  </div>
</template>

<script>
import {computed} from 'vue'
export default {
  name: 'formValidateView',
  data() {
    return {
      username: '',
      password: '',
    };
  },
  provide() {
    return {
      fom: computed(() => [])
    }
  },
  created() {
    this.$eventBus.$on('submit', this.test)
  },
  methods: {
    onFailed(errorInfo) {
      console.log('failed', errorInfo);
    },
    test() {
      // this.$refs.fom.submit()
      this.$refs.fom.validate().then(() => {
        
      })
      .catch((err) => {
        console.log(`output->err`,err)
      })
    },
    onSubmit() {

    }
  }
};
</script>
<style scoped></style>
