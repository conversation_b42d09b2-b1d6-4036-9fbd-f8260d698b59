<template>
  <div>
    <h1>FormDemo1View</h1>
    <el-form ref="ruleForm" :model="formData" :rules="rules">

      <el-descriptions labelStyle="width: 120px" class="margin-top" title="基本信息" :column="2" border>
        <el-descriptions-item>
          <template slot="label">
            昵称
          </template>
          <el-form-item prop="nickName">
            <el-input v-model="formData.nickName" placeholder="请输入昵称"></el-input>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item>
          <template slot="label">
            OA号
          </template>
          <el-form-item prop="oaCode">
            <el-input v-model="formData.oaCode" placeholder="请输入OA号"></el-input>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            所属团队
          </template>
          <el-form-item prop="teamName">
            <el-select v-model="formData.teamId" placeholder="请选择团队">
              <el-option v-for="team in teamData" :key="team.teamId" :label="team.teamName" :value="team.teamId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item>
          <template slot="label">
            头像
          </template>
          <el-form-item prop="avatar">
            <el-input v-model="formData.avatar" placeholder="请输入头像"></el-input>
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>


      <el-descriptions class="margin-top" title="电子名片" :column="2" border>
        <el-descriptions-item>
          <template slot="label">
            真实姓名
          </template>
          <el-form-item prop="name">
            <el-input v-model="formData.name" placeholder="请输入真实姓名"></el-input>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item>
          <template slot="label">
            英文名称/拼音
          </template>
          <el-form-item prop="enName">
            <el-input v-model="formData.enName" placeholder="请输入英文名称/拼音"></el-input>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item>
          <template slot="label">
            手机号码
          </template>
          <el-form-item prop="cellPhone">
            <el-input v-model="formData.cellPhone" placeholder="请输入手机号码"></el-input>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            半身像
          </template>
          <el-form-item prop="bust">
            <el-input v-model="formData.bust" placeholder="请输入半身像"></el-input>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            电子邮箱
          </template>
          <el-form-item prop="email">
            <el-input v-model="formData.email" placeholder="请输入电子邮箱"></el-input>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item>
          <template slot="label">
            所属公司
          </template>
          <el-form-item prop="company">
            <el-input v-model="formData.company" placeholder="请输入所属公司"></el-input>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item>
          <template slot="label">
            行政职级
          </template>
          <el-form-item prop="administrativeRank">
            <el-input v-model="formData.administrativeRank" placeholder="请输入行政职级"></el-input>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item>
          <template slot="label">
            专业职级
          </template>
          <el-form-item prop="professionalRank">
            <el-input v-model="formData.professionalRank" placeholder="请输入专业职级"></el-input>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item>
          <template slot="label">
            简介
          </template>
          <el-form-item prop="profile">
            <el-input v-model="formData.profile" placeholder="请输入简介"></el-input>
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>

      <el-descriptions class="margin-top" title="研究院主页" :column="1" border>
        <el-descriptions-item>
          <template slot="label">
            职位
          </template>
          <el-form-item prop="post">
            <el-input v-model="formData.post" placeholder="请输入职位"></el-input>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item>
          <template slot="label">
            毕业院校
          </template>
          <el-form-item prop="school">
            <el-input v-model="formData.school" placeholder="请输入毕业院校"></el-input>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item>
          <template slot="label">
            研究领域
          </template>
          <el-form-item prop="research">
            <el-input v-model="formData.research" placeholder="请输入研究领域"></el-input>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item>
          <template slot="label">
            项目经验
          </template>
          <el-form-item prop="experience">
            <el-input resize="none" type="textarea" v-model="formData.experience" placeholder="请输入项目经验"></el-input>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item>
          <template slot="label">
            资质荣誉
          </template>
          <el-form-item prop="honoraryBrief">
            <el-input resize="none" type="textarea" v-model="formData.honoraryBrief" placeholder="请输入资质荣誉"></el-input>
          </el-form-item>
        </el-descriptions-item>



      </el-descriptions>

      <el-descriptions class="margin-top" title="我的简介" :column="1" border>
        <el-descriptions-item>
          <template slot="label">
            个人简介
          </template>
          <el-form-item prop="personalProfile">
            <el-input resize="none" type="textarea" v-model="formData.personalProfile" placeholder="请输入个人简介"></el-input>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item>
          <template slot="label">
            研究成果
          </template>
          <el-form-item prop="results">
            <el-input resize="none" type="textarea" v-model="formData.results" placeholder="请输入研究成果"></el-input>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            资质荣誉详情
          </template>
          <el-form-item prop="honoraryDetail">
            <el-input resize="none" type="textarea" v-model="formData.honoraryDetail"
              placeholder="请输入资质荣誉详情"></el-input>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item>
          <template slot="label">
            附件
          </template>
          <el-form-item prop="attachment">
            <el-input v-model="formData.attachment" placeholder="请输入word附件"></el-input>
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
      <el-form-item>
        <el-button type="primary" @click="submitForm('ruleForm')">立即创建</el-button>
        <el-button @click="resetForm('ruleForm')">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'FormDemo1View',
  data() {
    return {
      formData: {
        nickName: "",//"昵称",
        oaCode: "",//"OA号",
        teamId: "",//"所属团队ID",
        avatar: "",//"头像",
        teamName: "",//"所属团队名称",
        bust: "",//"半身像",
        name: "",//"真实姓名",
        enName: "",//"英文名称/拼音",
        cellPhone: "",//"手机号码",
        email: "",//"电子邮箱",
        company: "",//"所属公司",
        administrativeRank: "",//"行政职级",
        professionalRank: "",//"专业职级",
        profile: "",//"简介",
        post: "",//"职位",
        school: "",//"毕业院校",
        research: "",//"研究领域",
        experience: "",//"项目经验",
        honoraryBrief: "",//"资质荣誉",
        personalProfile: "",//"个人简介",
        results: "",//"研究成果",
        honoraryDetail: "",//"资质荣誉详情",
        attachment: "",//"word附件"
      },
      rules: {
        avatar: [{ required: true, message: '请输入头像', trigger: 'blur' }],
        nickName: [{ required: true, message: '请输入昵称', trigger: 'blur' }],
        oaCode: [{ required: true, message: '请输入OA号', trigger: 'blur' }],
        teamId: [{ required: true, message: '请选择所属团队', trigger: 'blur' }],
        bust: [{ required: true, message: '请输入半身像', trigger: 'blur' }],
        name: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
        enName: [{ required: true, message: '请输入英文名称/拼音', trigger: 'blur' }],
        cellPhone: [{ required: true, message: '请输入手机号码', trigger: 'blur' }],
        email: [{ required: true, message: '请输入电子邮箱', trigger: 'blur' }],
        company: [{ required: true, message: '请输入所属公司', trigger: 'blur' }],
        administrativeRank: [{ required: true, message: '请输入行政职级', trigger: 'blur' }],
        professionalRank: [{ required: true, message: '请输入专业职级', trigger: 'blur' }],
        profile: [{ required: true, message: '请输入简介', trigger: 'blur' }],
        post: [{ required: true, message: '请输入职位', trigger: 'blur' }],
        school: [{ required: true, message: '请输入毕业院校', trigger: 'blur' }],
        research: [{ required: true, message: '请输入研究领域', trigger: 'blur' }],
        experience: [{ required: true, message: '请输入项目经验', trigger: 'blur' }],
        honoraryBrief: [{ required: true, message: '请输入资质荣誉', trigger: 'blur' }],
        personalProfile: [{ required: true, message: '请输入个人简介', trigger: 'blur' }],
        results: [{ required: true, message: '请输入研究成果', trigger: 'blur' }],
        honoraryDetail: [{ required: true, message: '请输入资质荣誉详情', trigger: 'blur' }],
        attachment: [{ required: true, message: '请输入word附件', trigger: 'blur' }]
      },
      teamData: [
        {
          teamId: 1,
          teamName: '团队1'
        },
        {
          teamId: 2,
          teamName: '团队2'
        }
      ]
    }
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          alert('submit!');
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    }
  }
};
</script>
<style scoped lang="scss">
::v-deep .el-descriptions-item__label{
  width:120px;
}
</style>
