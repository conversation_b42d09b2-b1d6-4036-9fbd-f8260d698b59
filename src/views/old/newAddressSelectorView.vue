<template>
  <div>
    <h1>newAddressSelectorView</h1>
    <div>activeIndex{{ activeIndex }}</div>
    <div>最终结果 {{ selectAddr }}</div>
    <button @click="show = true">show</button>

    <div class="address-box" style="height:300px;width: 420px;border: 0px solid red">
      <van-popup v-model="show" position="bottom" :style="{ height: '55%' }">
        <div class="address-title">
          <van-icon name="cross" class="cross-1" />
          请选择活动地点
          <van-icon name="cross" />
        </div>
        <van-tree-select height="100%" :items="items" :main-active-index.sync="activeIndex">
          <template #content>

            <div v-if="activeIndex === 0">
              <van-cell clickable v-for="item in companyInternal" :key="item.id" @click="confirmAddress3(item)">
                <template #title>
                  <span class="custom-title">{{ item.text }}</span>
                </template>
                <template #right-icon>
                  <van-icon v-show="item.id === companyInternalAddr.id" name="search" class="search-icon" />
                </template>
              </van-cell>
            </div>
            <div v-if="activeIndex === 1">
              <van-cell-group>
                <van-field v-model="addressDetail2" rows="1" :autosize="{ maxHeight: 40, minHeight: 20 }" label=""
                  type="textarea" placeholder="请填写详细地址" :rules="[{ required: true, message: '' }]" />
              </van-cell-group>

              <div style="display: flex;width: 100%;margin-top: 5px">
                <van-button style="margin-left:auto;height: 30px;width: 60px" type="info" @click="confirmAddress2">
                  确定
                </van-button>
              </div>
            </div>
            <div v-if="activeIndex === 2" class="address-inner-box">
              <van-search v-model="searchValue" placeholder="请输入省份名称…" enterkeyhint="search" />
              <div class="tree-box">
                <van-tree-select height="100%" :items="filterProvince" :main-active-index.sync="leftId"
                  :active-id.sync="rightId" />
              </div>

              <div class="address-detail">
                <van-form ref="myForm" @submit="onSubmit">
                  <van-field v-model="addressDetail" rows="1" :autosize="{ maxHeight: 40, minHeight: 20 }" label=""
                    type="textarea" placeholder="请填写详细地址" :rules="[{ required: true, message: '' }]" />
                </van-form>
              </div>

              <div style="display: flex;width: 100%">
                <van-button style="margin-left:auto;height: 30px;width: 60px" type="info" @click="confirmAddress">确定
                </van-button>

              </div>

            </div>

          </template>
        </van-tree-select>

      </van-popup>

    </div>

  </div>
</template>

<script>
import cityData from "@/data/province&city";

export default {
  name: 'newAddressSelectorView',
  data() {
    return {
      show: true,
      addressDetail: '',
      addressDetail2: '',
      searchValue: '',
      items: [
        {
          text: '公司内',
          id: '1',
        },
        {
          text: '兴业大厦\n(陆家嘴)',
          id: '2',
        }, {
          text: '省市',
          id: '3',

        },
      ],
      activeIndex: 2,
      transformedData: [],
      leftId: 1,
      rightId: 0,
      orginAddressList: [],
      companyInternal: [
        {
          text: '股票',
          id: 1,
        },
        {
          text: '黄金',
          id: 2,
        },
        {
          text: '债券',
          id: 3,
        },
        {
          text: '指数',
          id: 4,
        },
        {
          text: '信用',
          id: 5,
        },
      ],
      companyInternalAddr: '',
      selectAddr: {
        fieldValue: '',
        locationCode: [],
        detailAddr: '',
        show: false
      }
    }
  },
  mounted() {

    this.transformedData = cityData.map(item => ({
      id: item.code,
      text: item.name,
      children: item.children.map(child => ({
        text: child.name,
        id: child.code
      }))
    }));
    console.log(this.transformedData)
  },
  methods: {
    onSubmit() {
      
    },
    confirmAddress() {
     
        const formRef = this.$refs.myForm;
        console.log('outpuformRef',formRef)
      formRef.submit()
      if (this.rightId === 0) {
        this.$toast('请选择城市')
        return
      } else if (this.addressDetail === '') {
        this.$toast('请填写详细地址')
        return
      } else {
        const provinceCode = this.transformedData[this.leftId].id
        const cityCode = this.rightId

        const leftName = this.transformedData[this.leftId].text
        const rightName = this.transformedData[this.leftId].children.find(item => item.id === this.rightId).text

        this.selectAddr.fieldValue = leftName + '/' + rightName
        this.selectAddr.locationCode = [provinceCode, cityCode]
        this.selectAddr.detailAddr = this.addressDetail
        this.selectAddr.show = false
      }


    },
    confirmAddress2() {
     
      if (this.addressDetail2 === '') {
        this.$toast('请填写详细地址')
        return
      } else {
        const provinceCode = '1'
        const cityCode = '0'
        this.selectAddr.fieldValue = '兴业大厦'
        this.selectAddr.locationCode = [provinceCode, cityCode]
        this.selectAddr.detailAddr = this.addressDetail2
        this.selectAddr.show = false

      }
    },
    confirmAddress3(item) {

      console.log(item)
      this.companyInternalAddr = item
      const provinceCode = '0'
      const cityCode = '0'
      // this.companyInternal
      this.selectAddr.fieldValue = '公司内' + '/' + item.text
      this.selectAddr.locationCode = [provinceCode, cityCode]
      this.selectAddr.detailAddr = this.addressDetail2
      this.selectAddr.show = false
    }
  },
  watch: {},
  computed: {

    filterProvince() {
      if (!this.searchValue) {
        return this.transformedData
      } else {
        const newArray = this.transformedData.filter(item => item.text.includes(this.searchValue));
        return newArray
      }
    }
  }

};
</script>
<style scoped lang="scss">
::v-deep .address-box {

  /* 设置滚动条的宽度和颜色 */
  ::-webkit-scrollbar {
    // display: none;
    width: 3px;
  }

  ::-webkit-scrollbar-track {
    background: white;
  }

  ::-webkit-scrollbar-thumb {
    background: #4e6bff;
    border-radius: 10px;
    width: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}

.van-search__content {
  border-radius: 8px;
  //margin-bottom: -3px;
}

.van-button {
  font-size: 12px;
  margin-right: 10px;
  //border: 1px solid red;
}

::v-deep .van-tree-select__nav {
  background-color: white;

  .van-sidebar-item {
    background-color: white
  }
}

::v-deep .van-sidebar {}



.address-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  //border: 1px solid red;
  padding: 0 20px;

  .cross-1 {
    visibility: hidden;
  }
}

.van-sidebar-item--select::before {
  background-color: #01fd34;
}

::v-deep .van-sidebar-item__text {
  white-space: pre-wrap;
}

.address-inner-box {
  border: 1px solid red;
  display: flex;
  flex-direction: column;
  height: 300px;

  .tree-box {
    border: 1px solid red;
    //height: 100px;
    flex: 1 1 auto;
    overflow: auto;
  }
}

.address-detail {
  margin-top: 5px;

  .van-cell {

    //border: 1px solid darken(#f7f8fa,20%);
    //border: 1px solid #ffffff;
    border-radius: 5px;
    background-color: #f7f8fa;
  }

}
</style>
