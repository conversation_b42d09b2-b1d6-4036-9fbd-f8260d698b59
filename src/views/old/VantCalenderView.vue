<template>
  <div>
    <h1>VantCalenderView</h1>
    <van-cell value="内容" is-link @click="show = true" value-class="title-value">
      <!-- 使用 title 插槽来自定义标题 -->
      <template #title>
        <span class="custom-title">时间</span>
      </template>
      <span> <van-icon @click.stop="clearDate" v-if="date!==''" name="clear" style="margin-right: 10px;" />
        {{ date }}
        <span style="color: #cacacd;" v-if="date==''">请选择活动时间范围</span>
      </span>

    </van-cell>
    <van-calendar :min-date="new Date('2000-01-01')" :max-date="new Date('2030-01-01')" v-model="show" type="range"
      @confirm="onConfirm" />
  </div>
</template>

<script>
export default {
  name: 'VantCalenderView',
  data() {
    return {
      date: '2024/3/7 - 2024/3/15',
      show: false,
    }
  },
  methods: {
    clearDate() {
      this.date = ''
      
    },
    formatDate(date) {
      return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
    },
    onConfirm(date) {
      const [start, end] = date;
      let endTime = end.setHours(23, 59, 59, 999)
      
      this.show = false;
      this.date = `${this.formatDate(start)} - ${this.formatDate(end)}`;
      const startDate = start.getTime()
      console.log(`output->startdate`, endTime)
    },
  }
};
</script>
<style scoped>
.title-value {
  flex: 3
}
</style>
