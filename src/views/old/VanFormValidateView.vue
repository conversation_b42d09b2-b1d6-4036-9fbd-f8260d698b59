
<template>
  <div style="width: 300px;">
    <h1>VanFormValidateView</h1>
    <van-form ref="vanForm">
      <van-field placeholder="'111'" :value="nameLength" name="用户名" :rules="[{ required: true, message: '请填写用户名' }]">

      </van-field>
      <van-field placeholder="'222'" v-model="valuefield" name="fef" :rules="[{ required: true, message: '请填写' }]">

      </van-field>
      <div style="margin: 16px;">
        <van-button round block type="info" @click="onSubmit">提交</van-button>
      </div>
    </van-form>

    <van-cell value="内容" is-link>
      <!-- 使用 title 插槽来自定义标题 -->
      <template #right-icon>
        <span class="custom-title">单元格</span>
        <van-tag type="danger">标签</van-tag>
      </template> 

    </van-cell>
  </div>
</template>

<script>
export default {
  name: 'VanFormValidateView',
  data() {
    return {
      valuefield: '',
      username: [],
      password: '',
    }
  },
  methods: {

    onSubmit(values) {

      this.$refs.vanForm.validate().then(() => {
        console.log('submit')
      }).catch((err) => {
        console.log(`output->err`, err)
      })
    },
  },
  computed: {
    nameLength() {
      return this.username.length > 0 ? this.username.length : '';
    }
  }
};
</script>
<style lang="less" scoped>
/deep/.van-search {
  background-color: #f7f8fa
}
</style>
