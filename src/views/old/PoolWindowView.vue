<template>
  <div>
    <el-button type="primary" @click="openDialog" style="width: 200px;margin-bottom: 10px;">打开弹窗</el-button>


    <span  v-for="(groupedItems, poolName) in groupedData" :key="poolName">
      <el-tag closable @close="closeTag(groupedItems)" style="margin-right: 10px;" @click="openTag(groupedItems)">
        {{ generateSubscriptionText(groupedItems) }}
      </el-tag>
    </span>


    <PoolWindow :type="poolType" :poolId="currentPoolId" :visible.sync="visible" :selectedPerson="selectedPerson" @updatePerson="updatePerson">
    </PoolWindow>
  </div>
</template>
<script>
import PoolWindow from '@/components/PoolWindow'
export default {
  components: { PoolWindow },
  data() {
    return {
      currentPoolId: '',
      poolType: 'all',
      visible: false,
      selectedPerson: [],
     
    }
  }, computed: {
    groupedData() {
      if (this.selectedPerson.length > 0) {
        return this.selectedPerson.reduce((acc, item) => {
          if (!acc[item.poolId]) {
            acc[item.poolId] = [];
          }
          acc[item.poolId].push(item);
          return acc;
        }, {});
      } else {
        return []
      }
    }
  },
  methods: {
    openTag(tag) {
      console.log(`output->tag`, tag)
      this.currentPoolId = tag[0].poolId
      this.visible = !this.visible
    },
    closeTag(value) {
      console.log(`output->close`, value)
      const poolId= value[0].poolId
      this.selectedPerson = this.selectedPerson.filter(item => item.poolId !== poolId);



    },
    updatePerson(personData) {
      console.log(`output->确认人员`, personData)
      updateArrayOptimized(this.selectedPerson, personData)
      // this.selectedPerson = personData

    },
    openDialog() {
      this.visible = true
    },
    generateSubscriptionText(data) {
      if (data.length > 0) {
        if (data.length <= 5) {
          const names = data.map(item => item.userName).join(", ");
          return `${data[0].poolName}(${names})`;
        } else {
          const firstFiveNames = data.slice(0, 5).map(item => item.userName).join(", ");

          return `${data[0].poolName}(${firstFiveNames}等...${data.length}人)`;
        }
      } else {
        return ''
      }

    }
  }
}
function updateArrayOptimized(a, b) {
  // 创建哈希表用于快速查找
  const aHash = {};
  a.forEach(item => {
    const key = `${item.poolId}-${item.contactId}`;
    aHash[key] = item;
  });

  b.forEach(bItem => {
    const key = `${bItem.poolId}-${bItem.contactId}`;

    if (bItem.selected) {
      // 如果selected是true且在哈希表中找不到，添加到a并更新哈希表
      if (!aHash[key]) {
        a.push(bItem);
        aHash[key] = bItem;
      }
    } else {
      // 如果selected是false且在哈希表中能找到，从a删除并更新哈希表
      if (aHash[key]) {
        const index = a.findIndex(aItem => aItem.poolId === bItem.poolId && aItem.contactId === bItem.contactId);
        if (index !== -1) {
          a.splice(index, 1);
        }
        delete aHash[key];
      }
    }
  });

}
</script>
<style lang="">
  
</style>