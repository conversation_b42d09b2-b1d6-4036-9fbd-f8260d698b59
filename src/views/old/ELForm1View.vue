<template>
  <el-form ref="activityForm" :model="activity" :rules="rules" label-width="100px">
    <el-form-item label="活动名称" prop="name">
      <el-input v-model="activity.name"></el-input>
    </el-form-item>
    <div v-for="(item, index) in activity.targetInfo" :key="index" class="ojb-item">
      <el-form-item :label="'对象' + (index + 1)" :prop="`targetInfo.${index}.targetName`">
        <el-input v-model="item.targetName"></el-input>
      </el-form-item>
      <div v-for="(address, addrIndex) in item.addressList" class="ojb-item__addr" :key="'address' + addrIndex">
        <el-form-item label-width="60px" :label="'地址'" :prop="`targetInfo.${index}.addressList.${addrIndex}.addrCode`">
          <el-cascader v-model="item.addressList[addrIndex].addrCode" :options="regions" placeholder="请选择省市">
          </el-cascader>

        </el-form-item>
        <el-form-item label-width="10px" :prop="`targetInfo.${index}.addressList.${addrIndex}.detail`">

          <el-input style="width: 100%;" v-model="item.addressList[addrIndex].detail"></el-input>

        </el-form-item>
      </div>
      <div class="btn">
        <el-button size="small" @click="addTargetInfo">添加</el-button>
        <el-button size="small" :disabled="activity.targetInfo.length <= 1"
          @click="deleteTargetInfo(index)">删除</el-button>

      </div>
      <!-- <el-button @click="addItemAddress(index)">添加</el-button> -->
    </div>
    <el-form-item>
      <el-button type="primary" @click="submitForm">提交</el-button>
    </el-form-item>
    {{ activity }}
  </el-form>
</template>

<script>
export default {
  name: "YourComponentName",
  data() {
    return {
      activity: {
        targetInfo: [
          {
            targetName: '',
            addressList: [{
              addrCode: [],
              detail: ''

            }],
          }
        ],
        targetInfo2:[],
        name: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入活动名称', trigger: 'blur' }
        ],
        // 由于我们将为每个目标和地址生成动态的规则，这里不再需要静态定义
      },
      regions: [
        {
          value: '001',
          label: '北京',
          children: [
            { value: '001001', label: '北京市' },
            { value: '001002', label: '海淀区' },
            { value: '001003', label: '朝阳区' }
          ]
        },
        {
          value: '002',
          label: '上海',
          children: [
            { value: '002001', label: '上海市' },
            { value: '002002', label: '黄浦区' },
            { value: '002003', label: '徐汇区' }
          ]
        },
        {
          value: '003',
          label: '广东',
          children: [
            { value: '003001', label: '广州市' },
            { value: '003002', label: '天河区' },
            { value: '003003', label: '越秀区' }
          ]
        }
      ]
    };
  },
  created() {
    this.generateRules();
  },
  watch: {
    'activity.targetInfo': {
      handler(value) {
        console.log(`output->变化`, value)
        this.activity.targetInfo2=value.map((_, index) => {
          return {
            targetName: _.targetName,
            addressList: _.addressList.map((_, addrIndex) =>  {
              return {
                provinceCode: _.addrCode[0],
                cityCode: _.addrCode[1],
                detail: _.detail
              }
            })
          }
        })
      },
      deep: true
    }
  },
  methods: {
    addTargetInfo() {
      this.activity.targetInfo.push({
        targetName: '', addressList: [{
          addrCode: [],
          detail: ''

        }] });
      this.generateRules(); // 重新生成规则
    },
    deleteTargetInfo(index) {
      this.activity.targetInfo.splice(index, 1);
      this.generateRules(); // 重新生成规则
    },
    addItemAddress(index) {
      this.activity.targetInfo[index].addressList.push('');
      this.generateRules(); // 重新生成规则
    },
    generateRules() {
      this.rules.targetInfo = this.activity.targetInfo.map((_, index) => ({
        targetName: [
          { required: true, message: `请输入对象${index + 1}的名称`, trigger: 'blur' }
        ],
        addressList: _.addressList.map((_, addrIndex) =>  ({
          addrCode: [
            { required: true, message: `请填写地址`, trigger: 'blur' }
          ],
          detail: [
            { required: true, message: `请填写地址`, trigger: 'blur' }
          ]
        }))
      }));
    },
    submitForm() {
      this.$refs.activityForm.validate((valid) => {
        if (valid) {
          alert('提交成功!');
        } else {
          console.log('提交失败，请检查表单项!');
          return false;
        }
      });
    }
  }
};
</script>
<style lang="scss">
.ojb-item{
  
  display: flex;

  &__addr{
    display: flex;
    width: 100%;
    > :nth-child(2){
  
    flex-grow: 1;
 
    }
  }
  .btn{
    width: 200px;
  
  }
}
</style>