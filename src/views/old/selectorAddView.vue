<!--elem选择器可以输入添加-->
<template>
  <div>
    <div>选中值:{{ value }}</div>
    <div>另一个:{{ anotherVariable }}</div>
    <div style="position:relative;display: inline-block">

      <el-select @visible-change="visibleChange" :filter-method="filterMethod" v-model="value" filterable 
        default-first-option @keydown.enter.native="handleEnterKey" @change="onChange" placeholder="请选择文章标签">
        <el-option @mouseenter.native="handleMouseEnter(item)" v-for="item in filterOptions" :key="item.value"
          :label="item.label" :value="item.value">
        </el-option>
      </el-select>
      <span v-show="showAddButton" @click="addNewOption" class="span-add">新增</span>
      <!-- {{ filterOptions }} -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'selectorAddView',
  data() {
    return {
      options: [{
        value: 'HTMLvalue',
        label: 'HTML'
      }, {
        value: 'CSSvalue',
        label: 'css'
      }, {
        value: 'JavaScriptvalue',
        label: 'JavaScript'
        }
        , {
          value: 'test1value',
          label: 'test1'
        }, {
          value: 'test2value',
          label: 'test2'
        }],
      value: '',
      anotherVariable: '',
      query: '',
      showAddButton: false
    }
  },
  computed: {
    filterOptions() {
      return this.options.filter(item => {
        return item.label.indexOf(this.query) > -1
      })
    }
  },
  watch: {
    value(val) {
      const hasValue = this.options.some(item => item.value ===val);
      if (!hasValue) {
        this.anotherVariable = val
      }else{
        this.anotherVariable = ''
      }
    }
  },
  methods: {
    isNew(val) {
      const hasValue = this.options.some(item => item.value === val);
      if (!hasValue) {
        this.anotherVariable = val
      }else{
        this.anotherVariable = ''
      }
    },
    onChange(val) {

      this.isNew(val)
    },
    handleMouseEnter(item) {
      // console.log('hover值',item)
     const hasValue=this.options.some(item => item.value === this.value);
    },
    handleEnterKey() {
      this.isNew(this.value)
    },
    visibleChange(val) {
      if (val) {
        this.query = ''
      } else {
        this.showAddButton = false
      }

    },

    addNewOption() {

      this.value = this.query
      this.anotherVariable = this.query
      this.showAddButton = false
    },
    filterMethod(query) {

      this.query = query
      const exists = this.options.findIndex(item => item.label === query) >= 0
      this.showAddButton = !exists

    }
  }
};
</script>
<style scoped lang="scss">
.span-add {
  color: #409eff;
  position: absolute;
  right: 39px;
  top: 11px;
  font-size: 14px;
  cursor: pointer;

  &:hover {
    color: darken(#409eff, 10%)
  }
}
</style>
