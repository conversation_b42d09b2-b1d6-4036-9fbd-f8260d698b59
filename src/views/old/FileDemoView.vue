<template>
  <div>
    <h1>FileDemoView</h1>

    <div class="fileBox">
      <div class="file" @click="mainDownLoad(item)" v-for="item in mergeFilesWithSameOutUrl" :key="item.fileUrl">
        <div class="left">
          <div class="person-name">
            <div class="title" >姓名</div>
            <!-- <div class="tag-box">
              <div class="tag" style="visibility: hidden;">a</div>
            </div> -->
          </div>
          <div class="file-info">
            <div class="title">{{ getFileName(item[0]) }}</div>
            <div class="tag-box" v-if="getClass(item[0]) === 'word' || getClass(item[0]) === 'ppt'">
              <template v-for="(innerItem, index) in item">
                <!-- -->
                <div class="tag" :class="getClass(innerItem)" @click.stop="downLoad(innerItem)">
                  {{ getClass(innerItem) }}
                </div>
                <div class="tag pdf" v-show="convertBtnVisible(innerItem) && innerItem.conversion == '0'">
                  转换PDF
                </div>
                <div class="tag pdf" v-show="convertBtnVisible(innerItem) && innerItem.conversion == '2'">
                  刷新
                </div>
              </template>

            </div>
          </div>
        </div>
        <div class="right">
          <span @click.stop="deleteFile(item)">删除</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'FileDemoView',

  data() {
    return {

      files: [
        {
          "bucketName": "clientapp",
          "fileName": "API接口文档-20230331-3.0-20240418105921.xlsx",
          "fileOutUrl": "https://xxx/data/bucketName/API接口文档-20230331-3.0-20240418105921.xlsx",
          "correlationSpanExtend": "",
          "moduleSpan": "mainFile",
          "fileUrl": "http://xxx/clientapp/API接口文档-20230331-3.0-20240418105921.xlsx",
          "correlationId": "",
          "moduleId": "rese-1713494441730",
          "conversion": "2",
          "correlationSpan": "",
          "name": "API接口文档-20230331-3.0-20240418105921.xlsx"
        },
        {
          "bucketName": "clientapp",
          "fileName": "hello-20240418105928.pptx",
          "fileOutUrl": "https://xxx/data/bucketName/hello-20240418105928.pptx",
          "correlationSpanExtend": "",
          "moduleSpan": "mainFile",
          "fileUrl": "http://xxx/clientapp/hello-20240418105928.pptx",
          "correlationId": "",
          "moduleId": "rese-1713494441730",
          "conversion": "2",
          "correlationSpan": "",
          "name": "hello-20240418105928.pptx"
        },
        {
          "bucketName": "clientapp",
          "fileName": "hello-20240418105928.pdf",
          "fileOutUrl": "https://xxx/data/bucketName/hello-20240418105928.pptx",
          "correlationSpanExtend": "",
          "moduleSpan": "mainFile",
          "fileUrl": "http://xxx/clientapp/hello-20240418105928.pdf",
          "correlationId": "",
          "moduleId": "rese-1713494441730",
          "conversion": "1",
          "correlationSpan": "",
          "name": "hello-20240418105928.pdf"
        },
        {
          "bucketName": "clientapp",
          "fileName": "兴业研究管家“活动”菜单新增内容20230707-20240418105936.docx",
          "fileOutUrl": "https://xxx/data/bucketName/兴业研究管家“活动”菜单新增内容20230707-20240418105936.docx",
          "correlationSpanExtend": "",
          "moduleSpan": "mainFile",
          "fileUrl": "http://xxx/clientapp/兴业研究管家“活动”菜单新增内容20230707-20240418105936.docx",
          "correlationId": "",
          "moduleId": "rese-1713494441730",
          "conversion": "1",
          "correlationSpan": "",
          "name": "兴业研究管家“活动”菜单新增内容20230707-20240418105936.docx"
        },
        {
          "bucketName": "clientapp",
          "fileName": "兴业研究管家“活动”菜单新增内容20230707-20240418105936.pdf",
          "fileOutUrl": "https://xxx/data/bucketName/兴业研究管家“活动”菜单新增内容20230707-20240418105936.docx",
          "correlationSpanExtend": "",
          "moduleSpan": "mainFile",
          "fileUrl": "http://xxx/clientapp/兴业研究管家“活动”菜单新增内容20230707-20240418105936.pdf",
          "correlationId": "",
          "moduleId": "rese-1713494441730",
          "conversion": "1",
          "correlationSpan": "",
          "name": "兴业研究管家“活动”菜单新增内容20230707-20240418105936.pdf"
        }


      ]
    }
  },
  methods: {
    deleteFile(filesToDelete) {
      const filesToDeleteNames = filesToDelete.map(item => item.name)
      for (let i = this.files.length - 1; i >= 0; i--) {
        if (filesToDeleteNames.includes(this.files[i].name)) {
          this.files.splice(i, 1);
        }
      }


      // this.files = this.files.filter(item => !files.includes(item))
    },
    mainDownLoad(files) {
      let resFile = files[0]
      files.forEach(file => {
        const str = file.name
        const lastIndex = str.lastIndexOf('.');
        const type = str.slice(lastIndex + 1);
        if (type === 'pdf') {
          resFile = file
        }
      })
      this.downLoad(resFile)
    },
    downLoad(item) {
      console.log(`output->item`, item)
    },
    convertBtnVisible(item) {

      const fileTypes = ['.doc', '.docx', '.ppt', '.pptx']
      return fileTypes.some(fileType => item.name.toLowerCase().endsWith(fileType))
    },
    getFileName(item) {
      const str = item.name
      const lastIndex = str.lastIndexOf('.');
      const name = str.slice(0, lastIndex);
      const type = str.slice(lastIndex + 1);
      const specialType = ['ppt', 'pptx', 'doc', 'docx']
      if (specialType.includes(type)) {
        return name
      } return str

    },
    getClass(item) {
      const str = item.name
      const lastIndex = str.lastIndexOf('.');
      const type = str.slice(lastIndex + 1);
      if (type === 'doc' || type === 'docx') {
        return 'word'
      } else if (type === 'ppt' || type === 'pptx') {
        return 'ppt'
      } else if (type === 'xlsx' || type === 'xls') {
        return 'excel'

      }
      return type
    },

  },
  computed: {
    mergeFilesWithSameOutUrl() {
      const data = this.files
      if (data) {
        const groupedData = data.reduce((acc, current) => {
          const existingGroup = acc.find(group => group[0].fileOutUrl === current.fileOutUrl);
          if (existingGroup) {
            existingGroup.push(current);
          } else {
            acc.push([current]);
          }
          return acc;
        }, []);
        return groupedData
      } else {
        return []
      }



    }
  }
};
</script>
<style lang="less" scoped>
.fileBox {
  @word: #5077f3;
  @pdf: #cd5945;
  @ppt: #e8be6f;
  @excel: #3c6945;
  padding: 0px 10px;
  width: 400px;
  color: @word;

  .file {
    margin-bottom: 10px;

    display: flex;

    border-bottom: 1px solid rgba(211, 211, 211, 0.531);

    .left {
      flex: 1;
      display: flex;

      .person-name {
        color: black;
        border: 1px solid blue;
        .title{
          width: 40px;
        }
      }

      .file-info {
        flex: 1;

      }

      .title {
        word-break: break-all;
        cursor: pointer;
        padding-bottom: 10px;
      }

      .tag-box {

        padding-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: flex-end;


        .tag {
          line-height: 12px;
          cursor: pointer;
          margin-left: 7px;

          font-size: 12px;
          display: inline-block;
          padding: 2px 13px;
          border-radius: 17px;
          font-family: Arial, sans-serif;
          color: black;
          border: 1px solid black;

          &.pdf {
            color: @pdf;
            border: 1px solid @pdf;
          }

          &.word {
            color: @word;
            border: 1px solid @word;
          }

          &.ppt {
            color: @ppt;
            border: 1px solid @ppt;
          }

          &.excel {
            color: @excel;
            border: 1px solid @excel;
          }

        }
      }
    }

    .right {

      display: flex;
      align-items: center;
      margin-top: -10px;
    }


  }
}
</style>
