<template>
  <div>
    <van-button type="primary" @click="openPool">接收人组选择</van-button>

   
    <div class="emailList">
      <div class="selectEmail" v-for="(groupedItems, poolName) in groupedData()" :key="poolName">
        <span @click="checkPoolGroup(groupedItems)" class="emailAll">{{ generateSubscriptionText(groupedItems) }}</span>
        <img class="delete" alt="x" @click="closeTag(groupedItems)">
      </div>
    </div>


    <PoolPopupWindow ref="poolRef" @closeWindow="poolVisible = false" :visible="poolVisible"
      :personList.sync="poolDataList" :selectedPoolGroupData="selectedPoolGroupData" />
  </div>
</template>

<script>
import PoolPopupWindow from '@/components/PoolPopupWindow.vue'

export default {
  components: {
    PoolPopupWindow
  },
  name: 'PoolPopupView',
  data() {
    return {
      selectedPoolGroupData: [],
      poolVisible: false,
     
      poolDataList:[]
    };
  },
  mounted() {

  },
  computed: {

  },
  methods: {
    openPool() {
      this.poolVisible = !this.poolVisible
      this.selectedPoolGroupData=[]
    },
    checkPoolGroup(item) {
       this.selectedPoolGroupData = item
      this.poolVisible = !this.poolVisible
    },
    closeTag(value) {
      console.log(`output->关闭`)
      this.$refs.poolRef.deletePool(value)
    },
    groupedData() {
      if (this.poolDataList.length > 0) {
        return this.poolDataList.reduce((acc, item) => {
          if (!acc[item.poolId]) {
            acc[item.poolId] = [];
          }
          acc[item.poolId].push(item);
          return acc;
        }, {});
      } else {
        return []
      }
    },
    generateSubscriptionText(data) {
      if (data.length > 0) {
        if (data.length <= 5) {
          const names = data.map(item => item.emailUser).join(", ");
          return `${data[0].poolName}(${names})`;
        } else {
          const firstFiveNames = data.slice(0, 5).map(item => item.emailUser).join(", ");

          return `${data[0].poolName}(${firstFiveNames}等...${data.length}人)`;
        }
      } else {
        return ''
      }

    }

  }
};


</script>
<style lang="less" scoped>

.emailList {
  padding: 0 8px;
  // height: 70vh;
  overflow: scroll;
  background: #ffffff;

  .selectEmail {
    width: 100%;
    height: 50px;
    line-height: 50px;
    border-bottom: 1px solid #f7f8f9;
    position: relative;

    .recipient {
      font-size: 15px;
      color: #1d2341;
      letter-spacing: 0;
      font-weight: 500;
    }

    .recipientName {
      width: 30%;
      float: left;
      display: block;
      font-size: 14px;
      color: #1d2341;
      font-weight: 500;
      letter-spacing: 0;
      overflow: hidden;
      /*内容会被修剪，并且其余内容是不可见的*/
      text-overflow: ellipsis;
      /*显示省略符号来代表被修剪的文本。*/
      white-space: nowrap;
      /*文本不换行*/
    }

    .pleaseSelect {
      float: right;
      margin-right: 20px;
      font-size: 14px;
      color: #1d2341;
      letter-spacing: 0;
      font-weight: 400;
    }

    .email {
      width: 60%;
      float: right;
      display: block;
      margin-right: 30px;
      font-size: 14px;
      color: #1d2341;
      letter-spacing: 0;
      font-weight: 400;
      overflow: scroll;
    }

    .emailAll {
      width: 90%;
      display: block;
      font-size: 14px;
      color: #1d2341;
      letter-spacing: 0;
      font-weight: 400;
      overflow: scroll;
      white-space: nowrap;
    }

    img {
      width: 13px;
      height: 13px;
      position: absolute;
      top: 18px;
      right: 0;
    }
  }
}
</style>
