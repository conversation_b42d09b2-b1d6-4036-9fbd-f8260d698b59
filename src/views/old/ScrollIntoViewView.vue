<template>
  <div>
   
    <button @click="getTableinfo">@@</button>
    <el-table ref="personTable" :data="tableData" highlight-current-row @current-change="handleCurrentChange"
      style="width: 100%">
      <el-table-column prop="name" label="Name">
      </el-table-column>
      <el-table-column prop="age" label="Age">
      </el-table-column>
      <el-table-column prop="gender" label="Gender">
      </el-table-column>
      <el-table-column prop="inputValue" label="input">
        <template slot-scope="scope">
          <el-input v-model="scope.row.inputValue"></el-input>
        </template>
      </el-table-column>
    </el-table>

  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: []
    };
  },
  mounted() {
    setTimeout(() => {
      this.tableData = this.generateSampleData(100)
      this.$nextTick(() => {
        this.getTableinfo()
      })
    }, 1000);

  },
  methods: {
    handleCurrentChange(val) {
      // this.currentRow = val;
    },
    generateSampleData(count) {
      const data = [];
      for (let i = 1; i <= count; i++) {
        data.push({
          name: `Person ${i}`,
          age: Math.floor(Math.random() * 50) + 20,
          gender: i % 2 === 0 ? 'Male' : 'Female'
        });
      }
      return data;
    },
    getTableinfo() {
      const table = this.$refs.personTable.$el
      var elements = table.querySelectorAll("*");
      for (var i = 0; i < elements.length; i++) {
        if (elements[i].textContent === 'Person 5') {
          if (elements[i].tagName === 'TD') {
            var input = elements[i].parentElement.querySelectorAll("input")[0]
            console.log(`output-> input`, input.value)

          }


          elements[i].scrollIntoView({ behavior: "smooth", block: "center" });
          elements[i].parentElement.parentElement.style.backgroundColor = "rgba(179, 204, 242, 0.1)";
        }
      }
    }
  }
};
</script>

<style lang="scss">
/* Your SCSS styles go here */
.asdf {
  color: #b3ccf2;
}
</style>