<!--滚动加载elem级联选择器-->
<template>
  <div class="box">
    <el-cascader
        v-model="selectedOptions"
        :options="options"
        @change="handleCascaderChange"
        v-loadmore="loadMore"
        placeholder="请选择"
    ></el-cascader>
  </div>
</template>

<script>
import directives from '@/directives/directives.js'

export default {
  name: 'scrollLoadListView',
  components: {
  },
  created() {
    this.loadMore();
  },
  data() {
    return {
      selectedOptions: [], // 选中的选项
      options: [
        {
          value: '1',
          label: '选项1',
          children: [],
        }
      ],
      selected: "",
      currentPage: 1,
      pageSize: 15,
      loading: false, // 加载状态标志
    };
  },
  methods: {
    handleCascaderChange(value) {
      console.log('选中的值：', value);
    },
    loadMore() {
      console.log('helo')
      this.loading = true; // 开始加载数据
      setTimeout(() => {

      }, 1000);
      const newData = [];
      for (let i = 1; i <= this.pageSize; i++) {
        const newLabel = `选项 ${this.options.length + i}`;
        newData.push({
          value: newLabel,
          label: `模拟数据 ${newLabel}`,
          children: []
        });
      }

      this.options = [...this.options, ...newData];
      this.currentPage++;
      this.loading = false; // 数据加载完成
    }
  },
};
</script>

<style scoped lang="scss">
.box{

}
</style>
