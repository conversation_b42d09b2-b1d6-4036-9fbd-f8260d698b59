<!--不用插件实现虚拟滚动-->
<template>

  <div class="virtual-scroll-demo">
    <div class="scroll-container" @scroll="handleScroll" ref="virtualItemsContainer" >
      <div class="virtual-items-container" :style="{ height: totalHeight + 'px' }">
        <div style="position: absolute;width: 100%" ref="itemBox">
          <div class="virtual-item" ref="item" v-for="(item, index) in visibleItems" :key="index">
            {{ item }}
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      totalItems: 200, // 总项目数
      itemHeight: 50, // 每个虚拟项目的高度
      visibleItems: [], // 可见项目
      scrollTop: 0, // 滚动位置
    };
  },
  computed: {
    totalHeight() {
      return this.totalItems * this.itemHeight;
    },
  },
  methods: {
    getHt(){
      console.log(this.$refs.item[0].offsetHeight)
    },
    handleScroll(event) {
      this.scrollTop = event.target.scrollTop;
      this.updateVisibleItems();
    },
    updateVisibleItems() {
      window.requestAnimationFrame(() => {
        const startIndex = Math.floor(this.scrollTop / this.itemHeight);
        const endIndex = Math.min(
            Math.ceil((this.scrollTop + this.$el.clientHeight) / this.itemHeight),
            this.totalItems
        );
        this.visibleItems = [];
        // this.$refs.itemBox.style.top = `${this.scrollTop}px`;
        this.$refs.itemBox.style.top = `${startIndex*this.itemHeight}px`;
        for (let i = startIndex; i <= endIndex; i++) {
          this.visibleItems.push(`Item ${i}`);
        }
      })

    },
  },
  mounted() {
    this.updateVisibleItems();
  },
};
</script>

<style scoped>
*{
  box-sizing: border-box;
}
.virtual-scroll-demo {
  border: 1px solid red;
  width: 300px;
  height: 400px;
  overflow: auto;
}

.scroll-container {
  height: 100%;
  overflow: auto;
  border: 1px solid #ccc;
}

.virtual-items-container {
  position: relative;
}

.virtual-item {
  height: 50px;
  line-height: 50px;
  border-bottom: 1px solid #ddd;
}
</style>
