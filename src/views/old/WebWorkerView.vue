<template>
  <div class="wrapper">

    <button @click="startWorker">Start Worker</button>
    <button @click="startWorkerBlob">startWorkerBlob </button>
    <button @click="test">test </button>
    <p>Result: {{ result }}</p>
  </div>
</template>

<script>


export default {
  name: 'VanPopupInVancellView',
  data() {
    return {
      result: '',

    }
  },
  methods: {
    test() {
      console.log(`output->window.worker`,window.Worker)
    },
    startWorker() {
      // let result = 0;
      // for (let i = 0; i < 999999999; i++) {
      //   result += i;
      // }
      // this.result = result
      // console.log(`output->`, result)
      // return

      const worker = new Worker('worker.js');

      worker.postMessage(999999999); // 向Web Worker发送消息

      worker.onmessage = (event) => {
        this.result = event.data; // 接收Web Worker处理后的结果
        worker.terminate(); // 关闭Web Worker
      };
    },
    startWorkerBlob() {
      const workerCode = `
        self.onmessage = function(e) {
           const data = e.data;
          const result = processData(data);
     
          postMessage(result);
        }

        function processData(data) {
            let result = 0;
            for (let i = 0; i < data; i++) {
                result += i;
            }
            return result;
        }
      `;

      const blob = new Blob([workerCode], { type: 'application/javascript' });
      const worker = new Worker(URL.createObjectURL(blob));

      worker.postMessage(999999999);

      worker.onmessage = (e) => {
        this.result = e.data;
        worker.terminate();

      };

    },


  }
};
</script>
<style lang="scss"></style>
