<template>
  <div>
    <el-date-picker
      v-model="dateRange"
      type="daterange"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      style="width: 350px;"
    ></el-date-picker>
    <div style="margin-top: 16px;">
      你选择的日期范围：{{ dateRange }}
    </div>
    <el-button @click="fetchDateRange" style="margin-top: 16px;">模拟后台返回时间</el-button>
  </div>
</template>

<script>
export default {
  name: "TimePicker",
  data() {
    return {
      dateRange: []
    };
  },
  methods: {
    // 模拟后台返回时间
    fetchDateRange() {
          // 假设后台返回的数据
      
 
      const backendRange = "2024-04-03 10:35:22"
      this.dateRange = [backendRange,backendRange];
    }
  },
  // 你也可以在 mounted 钩子里设置
  // mounted() {
  //   this.fetchDateRange();
  // }
};
</script>

<style scoped>
/* 可根据需要自定义样式 */
</style>
