<template>
    <div class="">
      <el-popover placement="bottom" :width="800" v-model="popoverVisible" popper-class="hover-info-popover">
        <template #reference>
          <el-button type="primary" size="small">查看详情</el-button>
        </template>
        <div>test</div>
      </el-popover>
  
      <el-button style="margin-top: 100px;" @click="popoverVisible = !popoverVisible">点击</el-button>
    </div>
   
  </template>
  
  <script>
  export default {
    name: 'hoverInfoView',
    data() {
      return {
        popoverVisible: true,
     
      };
    },
    methods: {
  
    }
  }
  </script>
  
  <style lang="less" scoped>
  /* 使用::v-deep或/deep/（Vue2）来深度选择器 */
  ::v-deep .el-popover.hover-info-popover {
    padding: 30px !important;
  }
  </style>
  