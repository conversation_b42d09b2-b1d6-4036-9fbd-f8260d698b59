<template>
  <div class="test-container">
    <h2>数值字符串比较示例</h2>
    
    <div class="comparison-section">
      <div class="input-group">
        <label>第一个数值：</label>
        <input v-model="value1" placeholder="输入第一个数值" />
      </div>
      
      <div class="input-group">
        <label>第二个数值：</label>
        <input v-model="value2" placeholder="输入第二个数值" />
      </div>

      <div class="result">
        <p>字符串直接比较结果：{{ directCompare }}</p>
        <p>数值比较结果：{{ numericCompare }}</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Test',
  data() {
    return {
      value1: '99.000',
      value2: '99.0'
    }
  },
  computed: {
    // 直接字符串比较
    directCompare() {
      return this.value1 === this.value2 ? '相等' : '不相等'
    },
    // 转换为数值后比较
    numericCompare() {
      const num1 = parseFloat(this.value1)
      const num2 = parseFloat(this.value2)
      return num1 === num2 ? '相等' : '不相等'
    }
  }
}
</script>

<style scoped>
.test-container {
  padding: 20px;
}

.comparison-section {
  max-width: 500px;
  margin: 20px auto;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
}

.input-group {
  margin-bottom: 15px;
}

.input-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.input-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.result {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.result p {
  margin: 5px 0;
  font-size: 16px;
}
</style>
