<template>
  <div class="table-demo">
    {{ form }}
    <el-form :model="form" :rules="rules" ref="form">
      <el-table cell-class-name="cell-class-name" :data="tableData" border style="width: 100%" empty-text="暂无数据，稍后查询">
        <el-table-column width="380">
          <template slot="header">
            <span class="required-field">*</span>证券代码
          </template>
          <template slot-scope="scope">

            <el-form-item :prop="'tableData.' + scope.$index + '.securityCode'" :rules="rules.securityCode">
              <el-select v-model="scope.row.securityCode" placeholder="请选择证券代码">
                <el-option v-for="item in securityCodeOptions" :key="item.value" :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="shortName" label="扩位简称" width="180" :formatter="formatters.defaultFormatter">
        
        </el-table-column>
        <el-table-column prop="fundType" label="基金类型" width="180" :formatter="formatters.defaultFormatter">
       
        </el-table-column>
        <el-table-column label="IOPV发布方" width="180">
          <template slot-scope="scope">
            <el-form-item :prop="'tableData.' + scope.$index + '.iopvPublisher'">
              <el-select v-model="scope.row.iopvPublisher" placeholder="请选择发布方">
                <el-option v-for="item in publisherOptions" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="IOPV发布起始日" width="180">
          <template slot-scope="scope">
            <el-form-item :prop="'tableData.' + scope.$index + '.startDate'">
              <el-date-picker v-model="scope.row.startDate" type="date" placeholder="选择日期" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="handleDelete(scope.$index)"
              :style="{ color: scope.$index === 0 ? '' : '#F56C6C' }" :disabled="scope.$index === 0">
              删除
            </el-button>
          </template>
        </el-table-column>

      </el-table>
      <el-table :show-header="false" :data="[{}]" border style="width: 100%">
        <el-table-column>
          <template>
            <el-button type="text" style="width: 100%" @click="addRow" :disabled="tableData.length >= 3">添加</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="form-actions">
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="resetForm">重置</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'TableDemo',
  data() {
    return {
      tableData: [],
      form: {
        tableData: []
      },
      formatters: {
        defaultFormatter: (row, column, cellValue) => {
          return cellValue || '2-'
        }
      },
      rules: {
        securityCode: [
          { required: true, message: '请选择证券代码', trigger: 'change' }
        ],
        shortName: [
          { required: true, message: '扩位简称不能为空', trigger: 'blur' }
        ],
        fundType: [
          { required: true, message: '基金类型不能为空', trigger: 'blur' }
        ],
        iopvPublisher: [
          { required: true, message: '请选择IOPV发布方', trigger: 'change' }
        ],
        startDate: [
          { required: true, message: '请选择IOPV发布起始日', trigger: 'change' }
        ]
      },
      securityCodeOptions: [
        { value: '000001', label: '000001' },
        { value: '000002', label: '000002' },
        { value: '000003', label: '000003' }
      ],
      publisherOptions: [
        { value: '发布方A', label: '发布方A' },
        { value: '发布方B', label: '发布方B' },
        { value: '发布方C', label: '发布方C' }
      ]
    }
  },
  watch: {
    tableData: {
      handler(val) {
        this.form.tableData = val;
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 添加新行
    addRow() {
      this.tableData.push({
        securityCode: '',
        shortName: '',
        fundType: '',
        iopvPublisher: '',
        startDate: ''
      });
    },
    // 删除行
    handleDelete(index) {
      this.tableData.splice(index, 1);
    },
    // 模拟从接口获取数据
    fetchData() {
      // 实际项目中这里会调用API
      // this.axios.get('/api/data').then(res => {
      //   this.tableData = res.data;
      // });
    },
    // 提交表单
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$message.success('表单验证通过');
          console.log('表单数据:', this.tableData);
          // 这里可以添加提交到后端的逻辑
        } else {
          this.$message.error('请完善表单信息');
          return false;
        }
      });
    },
    // 重置表单
    resetForm() {
      this.$refs.form.resetFields();
    }
  },
  mounted() {
    // 初始化时添加一行空数据
    this.addRow();
  }
}
</script>

<style scope>
.table-demo {
  padding: 20px;
}

.add-row-btn {
  margin-top: 20px;
  text-align: center;
}

.required-field {
  color: #F56C6C;
  margin-right: 2px;
}

.form-actions {
  margin-top: 20px;
  text-align: right;
}

.el-table__cell {
  border: 1px solid pink;
  /* padding-bottom: -10px; */
}

.el-form-item {
  margin-bottom: 0;
  padding: 0;
}

.el-form-item__content {
  margin-top: 23px;
  margin-bottom: 23px;
}
</style>
