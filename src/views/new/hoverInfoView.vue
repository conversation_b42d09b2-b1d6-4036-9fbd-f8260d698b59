<template>
  <div class="">
    <el-popover placement="bottom" trigger="click" popper-class="hover-info-popover">
      <template #reference>
        <el-button type="primary" size="small">查看详情</el-button>
      </template>
      <el-table :data="successTableData" border style="width: 100%" :cell-style="cellStyle"
        :header-cell-style="rowClass">
        <el-table-column prop="name" label="" width="280px"></el-table-column>
        <el-table-column prop="platform" label="竞价平台" width="160"></el-table-column>
        <el-table-column prop="fund" label="业管基金" width="260"></el-table-column>
      </el-table>
    </el-popover>

    <el-button @click="popoverVisible = !popoverVisible">点击</el-button>
  </div>

</template>

<script>
export default {
  name: 'hoverInfoView',
  data() {
    return {
      popoverVisible: true,
      successTableData: [
        { name: '配售比例', platform: '1234', fund: '5678' },
        { name: '比例配售日', platform: '2023-10-01', fund: '2023-10-01' },
        { name: '未日前有效申报的订单量', platform: '45678912', fund: '78945612' },
        { name: '未日前分配的订单量', platform: '12345678', fund: '12345678' },
        { name: '未日当天有效申报的订单量', platform: '98765432', fund: '98765432' },
        { name: '未日当天分配的订单量', platform: '34567891', fund: '34567891' },
        { name: '未日后有效申报的订单量', platform: '43219876', fund: '43219876' },
        { name: '未日后分配的订单量', platform: '89123456', fund: '89123456' }
      ]
    };
  },
  methods: {
    //设置表头的颜色
    rowClass({ row, rowIndex }) {
      console.log(rowIndex) //表头行标号为0
      return 'background:#e9ebef'
    },
    //设置指定行、列、具体单元格颜色
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) { // 指定坐标rowIndex ：行，columnIndex ：列
        return 'background:#e9ebef;width:360px';
      } else if (
        (column.property === 'platform' || column.property === 'fund') &&
        row.platform !== row.fund
      ) {
        // platform和fund不一致时，设置红色背景和字体
        return 'background:#fef0f0;color:#e32c2c;';
      } else {
        return '';
      }
    }
  }
}
</script>

<style lang="less" scoped>


.description {
  margin-bottom: 20px;
  color: #666;
}

.success {
  .status-icon {
    background-color: #67c23a;
    color: white;
  }
}

.error {
  .status-icon {
    background-color: #f56c6c;
    color: white;
  }
}

.el-table--border,
.el-table--group {
  border: 1px solid #ebeef5;
}
</style>
<style lang="less">
// :deep(.hover-info-popover) {
//   padding: 30px;


// }

.hover-info-popover {
  padding: 10px;
  .el-table {
    margin-top: 0;

    .el-table__cell {
      padding: 0;
      height: 45px;
    }
  }
}
</style>
