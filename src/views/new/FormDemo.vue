<template>
  <div class="form-demo">
    <h1>form validate demo</h1>
    <el-card class="form-card">
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="姓名" prop="name">
          <form-input v-model="ruleForm.name" placeholder="请输入姓名" style="width: 300px;"></form-input>
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="ruleForm.email" placeholder="请输入邮箱"></el-input>
        </el-form-item>
        
        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="ruleForm.phone" placeholder="请输入手机号码"></el-input>
        </el-form-item>
        
        <el-form-item label="年龄" prop="age">
          <el-input-number v-model="ruleForm.age" :min="0" :max="120"></el-input-number>
        </el-form-item>
        
        <el-form-item label="职业" prop="profession">
          <el-select v-model="ruleForm.profession" placeholder="请选择职业">
            <el-option label="程序员" value="programmer"></el-option>
            <el-option label="设计师" value="designer"></el-option>
            <el-option label="教师" value="teacher"></el-option>
            <el-option label="医生" value="doctor"></el-option>
            <el-option label="其他" value="other"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="兴趣爱好" prop="hobbies">
          <el-checkbox-group v-model="ruleForm.hobbies">
            <el-checkbox label="阅读"></el-checkbox>
            <el-checkbox label="编程"></el-checkbox>
            <el-checkbox label="运动"></el-checkbox>
            <el-checkbox label="音乐"></el-checkbox>
            <el-checkbox label="旅行"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="出生日期" prop="birthday">
          <el-date-picker
            v-model="ruleForm.birthday"
            type="date"
            placeholder="选择日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          ></el-date-picker>
        </el-form-item>
        
        <el-form-item label="个人简介" prop="description">
          <el-input
            type="textarea"
            v-model="ruleForm.description"
            :rows="4"
            placeholder="请输入个人简介"
          ></el-input>
        </el-form-item>
        
        <el-form-item label="同意协议" prop="agreement">
          <el-switch v-model="ruleForm.agreement"></el-switch>
          <span class="agreement-text">我已阅读并同意服务条款</span>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
          <el-button @click="resetForm('ruleForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'FormDemo',
  data() {
    // 自定义手机号码验证规则
    const validatePhone = (rule, value, callback) => {
      const phoneRegex = /^1[3-9]\d{9}$/
      if (!value) {
        callback(new Error('请输入手机号码'))
      } else if (!phoneRegex.test(value)) {
        callback(new Error('请输入有效的手机号码'))
      } else {
        callback()
      }
    }
    
    // 自定义邮箱验证规则
    const validateEmail = (rule, value, callback) => {
      const emailRegex = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
      if (!value) {
        callback(new Error('请输入邮箱'))
      } else if (!emailRegex.test(value)) {
        callback(new Error('请输入有效的邮箱地址'))
      } else {
        callback()
      }
    }
    
    return {
      ruleForm: {
        name: '',
        email: '',
        phone: '',
        age: 18,
        profession: '',
        hobbies: [],
        birthday: '',
        description: '',
        agreement: false
      },
      rules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { validator: validateEmail, trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ],
        age: [
          { required: true, message: '请输入年龄', trigger: 'change' },
          { type: 'number', min: 0, max: 120, message: '年龄必须在 0 到 120 之间', trigger: 'change' }
        ],
        profession: [
          { required: true, message: '请选择职业', trigger: 'change' }
        ],
        hobbies: [
          { type: 'array', min: 1, message: '请至少选择一个兴趣爱好', trigger: 'change' }
        ],
        birthday: [
          { required: true, message: '请选择出生日期', trigger: 'change' }
        ],
        description: [
          { required: true, message: '请填写个人简介', trigger: 'blur' },
          { min: 5, max: 200, message: '个人简介长度在 5 到 200 个字符', trigger: 'blur' }
        ],
        agreement: [
          { validator: (rule, value, callback) => {
            if (value === false) {
              callback(new Error('请同意服务条款'))
            } else {
              callback()
            }
          }, trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$message.success('表单提交成功')
          console.log('表单数据:', this.ruleForm)
        } else {
          this.$message.error('表单验证失败')
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.$message.info('表单已重置')
    }
  }
}
</script>

<style scoped>
.form-demo {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.form-card {
  margin-top: 20px;
}

.agreement-text {
  margin-left: 10px;
  font-size: 14px;
  color: #606266;
}

.el-form-item {
  margin-bottom: 22px;
}
</style>
