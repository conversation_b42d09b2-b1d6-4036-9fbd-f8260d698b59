<template>
  <div class="popover-demo">
    <h2>Popover demo</h2>
    <el-popover
      placement="top"
      width="200"
      trigger="hover"
      @show="handlePopoverShow"
      @hide="handlePopoverHide"
    >
      <template #reference>
        <el-button>点击显示 Popover</el-button>
      </template>
      <div>这是 Popover 的内容区域，当它显示时会触发事件</div>
    </el-popover>
    
    <div class="event-log" v-if="eventLog.length">
      <h3>事件日志:</h3>
      <ul>
        <li v-for="(log, index) in eventLog" :key="index">{{ log }}</li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PopoverDemo',
  data() {
    return {
      eventLog: []
    }
  },
  methods: {
    handlePopoverShow() {
      this.eventLog.push(`Popover 已显示 - ${new Date().toLocaleTimeString()}`)
      console.log('Popover 显示事件触发')
      // 这里可以执行任何需要的操作
    },
    handlePopoverHide() {
      this.eventLog.push(`Popover 已隐藏 - ${new Date().toLocaleTimeString()}`)
      console.log('Popover 隐藏事件触发')
    }
  }
}
</script>

<style scoped>
.popover-demo {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
}

.event-log {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border: 1px solid #eaeaea;
  border-radius: 4px;
}

.event-log h3 {
  margin-top: 0;
  margin-bottom: 10px;
}

.event-log ul {
  padding-left: 20px;
}
</style>
