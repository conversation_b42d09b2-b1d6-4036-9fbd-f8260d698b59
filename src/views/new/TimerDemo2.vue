<template>
  <div class="timer-container">
    <el-date-picker
      v-model="startTime"
      type="datetime"
      placeholder="选择开始时间"
      value-format="yyyy-MM-dd HH:mm:ss"
      :picker-options="pickerOptions">
    </el-date-picker>

    <el-date-picker
      v-model="endTime"
      type="datetime"
      placeholder="选择结束时间"
      value-format="yyyy-MM-dd HH:mm:ss"
      :picker-options="pickerOptions">
    </el-date-picker>

    <div class="comparison-result" v-if="startTime && endTime">
      {{ compareTimes() }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'TimerDemo2',
  data() {
    return {
      startTime: '',
      endTime: '',
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  methods: {
    compareTimes() {
      if (!this.startTime || !this.endTime) {
        return '请选择开始和结束时间'
      }
      
      const start = new Date(this.startTime)
      const end = new Date(this.endTime)
      
      if (start.getTime() === end.getTime()) {
        return '两个时间相等'
      } else {
        return '两个时间不相等'
      }
    }
  }
}
</script>

<style scoped>
.timer-container {
  display: flex;
  gap: 20px;
  padding: 20px;
  flex-direction: column;
}

.comparison-result {
  margin-top: 10px;
  color: #666;
}
</style>