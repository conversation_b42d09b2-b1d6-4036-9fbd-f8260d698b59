<template>
  <div class="advanced-form-demo">
    <h1>高级表单示例</h1>
    
    <!-- 基础信息表单 -->
    <el-card class="form-section">
      <div slot="header">
        <span>基础信息</span>
      </div>
      <el-form
        :model="basicForm"
        :rules="basicRules"
        ref="basicForm"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="basicForm.username" placeholder="请输入用户名">
                <i slot="prefix" class="el-icon-user"></i>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="真实姓名" prop="realName">
              <el-input v-model="basicForm.realName" placeholder="请输入真实姓名" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="basicForm.email" placeholder="请输入邮箱">
                <i slot="prefix" class="el-icon-message"></i>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="basicForm.phone" placeholder="请输入手机号">
                <i slot="prefix" class="el-icon-phone"></i>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-radio-group v-model="basicForm.gender">
                <el-radio label="male">男</el-radio>
                <el-radio label="female">女</el-radio>
                <el-radio label="other">其他</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出生日期" prop="birthday">
              <el-date-picker
                v-model="basicForm.birthday"
                type="date"
                placeholder="选择出生日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 地址信息 -->
    <el-card class="form-section">
      <div slot="header">
        <span>地址信息</span>
      </div>
      <el-form
        :model="addressForm"
        :rules="addressRules"
        ref="addressForm"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="省份" prop="province">
              <el-select v-model="addressForm.province" placeholder="请选择省份" @change="handleProvinceChange">
                <el-option
                  v-for="item in provinces"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="城市" prop="city">
              <el-select v-model="addressForm.city" placeholder="请选择城市" :disabled="!addressForm.province">
                <el-option
                  v-for="item in cities"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="区县" prop="district">
              <el-select v-model="addressForm.district" placeholder="请选择区县" :disabled="!addressForm.city">
                <el-option
                  v-for="item in districts"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="详细地址" prop="detail">
          <el-input v-model="addressForm.detail" placeholder="请输入详细地址" />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 动态表单字段 -->
    <el-card class="form-section">
      <div slot="header">
        <span>工作经历
          <el-button type="text" @click="addWorkExperience" style="margin-left: 10px">
            <i class="el-icon-plus"></i> 添加
          </el-button>
        </span>
      </div>
      <el-form
        :model="workForm"
        ref="workForm"
        label-width="120px"
      >
        <div v-for="(work, index) in workForm.experiences" :key="index" class="work-item">
          <el-divider v-if="index > 0" />
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                :prop="`experiences.${index}.company`"
                :rules="{ required: true, message: '请输入公司名称', trigger: 'blur' }"
                label="公司名称"
              >
                <el-input v-model="work.company" placeholder="请输入公司名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :prop="`experiences.${index}.position`"
                :rules="{ required: true, message: '请输入职位', trigger: 'blur' }"
                label="职位"
              >
                <el-input v-model="work.position" placeholder="请输入职位" />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                :prop="`experiences.${index}.startDate`"
                :rules="{ required: true, message: '请选择开始时间', trigger: 'change' }"
                label="开始时间"
              >
                <el-date-picker
                  v-model="work.startDate"
                  type="month"
                  placeholder="选择开始时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :prop="`experiences.${index}.endDate`"
                label="结束时间"
              >
                <el-date-picker
                  v-model="work.endDate"
                  type="month"
                  placeholder="选择结束时间（在职可不选）"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item
            :prop="`experiences.${index}.description`"
            label="工作描述"
          >
            <el-input
              type="textarea"
              v-model="work.description"
              :rows="3"
              placeholder="请描述你的工作内容"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="danger" size="small" @click="removeWorkExperience(index)">
              <i class="el-icon-delete"></i> 删除
            </el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-card>

    <!-- 文件上传 -->
    <el-card class="form-section">
      <div slot="header">
        <span>文件上传</span>
      </div>
      <el-form label-width="120px">
        <el-form-item label="头像上传">
          <el-upload
            class="avatar-uploader"
            action="https://jsonplaceholder.typicode.com/posts/"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="avatarUrl" :src="avatarUrl" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        
        <el-form-item label="附件上传">
          <el-upload
            class="upload-demo"
            action="https://jsonplaceholder.typicode.com/posts/"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            multiple
            :limit="3"
            :on-exceed="handleExceed"
            :file-list="fileList"
          >
            <el-button size="small" type="primary">
              <i class="el-icon-upload"></i> 点击上传
            </el-button>
            <div slot="tip" class="el-upload__tip">只能上传 jpg/png 文件，且不超过 500kb</div>
          </el-upload>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表单验证结果 -->
    <el-card class="form-section">
      <div slot="header">
        <span>其他信息</span>
      </div>
      <el-form
        :model="otherForm"
        :rules="otherRules"
        ref="otherForm"
        label-width="120px"
      >
        <el-form-item label="技能标签" prop="skills">
          <el-select
            v-model="otherForm.skills"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请输入技能标签"
          >
            <el-option
              v-for="item in skillOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="个人网站" prop="website">
          <el-input v-model="otherForm.website" placeholder="请输入个人网站">
            <template slot="prepend">http://</template>
          </el-input>
        </el-form-item>
        
        <el-form-item label="GitHub" prop="github">
          <el-input v-model="otherForm.github" placeholder="请输入GitHub用户名">
            <template slot="prepend">github.com/</template>
          </el-input>
        </el-form-item>
        
        <el-form-item label="通知设置" prop="notifications">
          <el-checkbox-group v-model="otherForm.notifications">
            <el-checkbox label="email">邮件通知</el-checkbox>
            <el-checkbox label="sms">短信通知</el-checkbox>
            <el-checkbox label="push">推送通知</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 提交按钮 -->
    <div class="form-actions">
      <el-button type="primary" size="large" @click="submitAllForms">
        <i class="el-icon-check"></i> 提交所有表单
      </el-button>
      <el-button size="large" @click="resetAllForms">
        <i class="el-icon-refresh"></i> 重置所有表单
      </el-button>
      <el-button type="info" size="large" @click="validateAllForms">
        <i class="el-icon-document-checked"></i> 验证所有表单
      </el-button>
    </div>
  </div>
</template>

<script>
import { provinceAndCityData } from '@/data/province&city.js'

export default {
  name: 'AdvancedFormDemo',
  data() {
    // 自定义验证规则
    const validateWebsite = (rule, value, callback) => {
      if (!value) {
        callback()
        return
      }
      const urlRegex = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w\.-]*)*\/?$/
      if (!urlRegex.test(value)) {
        callback(new Error('请输入有效的网址'))
      } else {
        callback()
      }
    }

    const validateGithub = (rule, value, callback) => {
      if (!value) {
        callback()
        return
      }
      const githubRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]){0,38}$/
      if (!githubRegex.test(value)) {
        callback(new Error('请输入有效的GitHub用户名'))
      } else {
        callback()
      }
    }

    return {
      // 基础信息
      basicForm: {
        username: '',
        realName: '',
        email: '',
        phone: '',
        gender: '',
        birthday: ''
      },
      basicRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' },
          { pattern: /^[a-zA-Z0-9_]+$/, message: '只能包含字母、数字和下划线', trigger: 'blur' }
        ],
        realName: [
          { required: true, message: '请输入真实姓名', trigger: 'blur' },
          { min: 2, max: 10, message: '长度在 2 到 10 个字符', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        gender: [
          { required: true, message: '请选择性别', trigger: 'change' }
        ],
        birthday: [
          { required: true, message: '请选择出生日期', trigger: 'change' }
        ]
      },

      // 地址信息
      addressForm: {
        province: '',
        city: '',
        district: '',
        detail: ''
      },
      addressRules: {
        province: [{ required: true, message: '请选择省份', trigger: 'change' }],
        city: [{ required: true, message: '请选择城市', trigger: 'change' }],
        district: [{ required: true, message: '请选择区县', trigger: 'change' }],
        detail: [{ required: true, message: '请输入详细地址', trigger: 'blur' }]
      },

      // 工作经历
      workForm: {
        experiences: [
          {
            company: '',
            position: '',
            startDate: '',
            endDate: '',
            description: ''
          }
        ]
      },

      // 其他信息
      otherForm: {
        skills: [],
        website: '',
        github: '',
        notifications: []
      },
      otherRules: {
        skills: [
          { type: 'array', max: 5, message: '最多选择5个技能标签', trigger: 'change' }
        ],
        website: [
          { validator: validateWebsite, trigger: 'blur' }
        ],
        github: [
          { validator: validateGithub, trigger: 'blur' }
        ]
      },

      // 省市数据
      provinces: [],
      cities: [],
      districts: [],

      // 技能选项
      skillOptions: [
        { value: 'JavaScript', label: 'JavaScript' },
        { value: 'Vue', label: 'Vue' },
        { value: 'React', label: 'React' },
        { value: 'Node.js', label: 'Node.js' },
        { value: 'Python', label: 'Python' },
        { value: 'Java', label: 'Java' },
        { value: 'PHP', label: 'PHP' },
        { value: 'Go', label: 'Go' }
      ],

      // 文件上传
      avatarUrl: '',
      fileList: []
    }
  },
  created() {
    this.initProvinces()
  },
  methods: {
    // 初始化省份数据
    initProvinces() {
      this.provinces = Object.keys(provinceAndCityData).map(key => ({
        value: key,
        label: provinceAndCityData[key].name
      }))
    },

    // 省份变化处理
    handleProvinceChange(provinceCode) {
      this.addressForm.city = ''
      this.addressForm.district = ''
      this.cities = []
      this.districts = []
      
      if (provinceCode && provinceAndCityData[provinceCode]) {
        this.cities = Object.keys(provinceAndCityData[provinceCode].children || {}).map(key => ({
          value: key,
          label: provinceAndCityData[provinceCode].children[key].name
        }))
      }
    },

    // 城市变化处理
    handleCityChange(cityCode) {
      this.addressForm.district = ''
      this.districts = []
      
      const provinceCode = this.addressForm.province
      if (provinceCode && cityCode && provinceAndCityData[provinceCode] && provinceAndCityData[provinceCode].children[cityCode]) {
        this.districts = Object.keys(provinceAndCityData[provinceCode].children[cityCode].children || {}).map(key => ({
          value: key,
          label: provinceAndCityData[provinceCode].children[cityCode].children[key].name
        }))
      }
    },

    // 添加工作经历
    addWorkExperience() {
      this.workForm.experiences.push({
        company: '',
        position: '',
        startDate: '',
        endDate: '',
        description: ''
      })
    },

    // 删除工作经历
    removeWorkExperience(index) {
      if (this.workForm.experiences.length > 1) {
        this.workForm.experiences.splice(index, 1)
      } else {
        this.$message.warning('至少需要一条工作经历')
      }
    },

    // 文件上传处理
    handleAvatarSuccess(res, file) {
      this.avatarUrl = URL.createObjectURL(file.raw)
    },

    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG && !isPNG) {
        this.$message.error('上传头像图片只能是 JPG/PNG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
      }
      return (isJPG || isPNG) && isLt2M
    },

    handlePreview(file) {
      console.log('Preview:', file)
    },

    handleRemove(file, fileList) {
      console.log('Remove:', file, fileList)
    },

    beforeRemove(file) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },

    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },

    // 验证所有表单
    validateAllForms() {
      const promises = [
        this.$refs.basicForm?.validate(),
        this.$refs.addressForm?.validate(),
        this.$refs.workForm?.validate(),
        this.$refs.otherForm?.validate()
      ]
      
      Promise.all(promises.map(p => p || Promise.resolve()))
        .then(() => {
          this.$message.success('所有表单验证通过！')
        })
        .catch(() => {
          this.$message.error('表单验证未通过，请检查填写内容！')
        })
    },

    // 提交所有表单
    submitAllForms() {
      this.validateAllForms()
      
      setTimeout(() => {
        const allData = {
          basic: this.basicForm,
          address: this.addressForm,
          work: this.workForm,
          other: this.otherForm,
          avatar: this.avatarUrl,
          attachments: this.fileList
        }
        
        console.log('提交的所有数据:', allData)
        this.$message.success('表单提交成功！数据已打印到控制台')
      }, 100)
    },

    // 重置所有表单
    resetAllForms() {
      this.$refs.basicForm?.resetFields()
      this.$refs.addressForm?.resetFields()
      this.$refs.workForm?.resetFields()
      this.$refs.otherForm?.resetFields()
      
      this.workForm.experiences = [{
        company: '',
        position: '',
        startDate: '',
        endDate: '',
        description: ''
      }]
      
      this.avatarUrl = ''
      this.fileList = []
      
      this.$message.info('所有表单已重置')
    }
  }
}
</script>

<style scoped>
.advanced-form-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.form-section {
  margin-bottom: 20px;
}

.work-item {
  padding: 10px 0;
}

.form-actions {
  text-align: center;
  margin-top: 30px;
}

.form-actions .el-button {
  margin: 0 10px;
}

.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 178px;
  height: 178px;
}

.avatar-uploader:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.upload-demo {
  max-width: 400px;
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 7px;
}

.el-divider--horizontal {
  margin: 20px 0;
}
</style>