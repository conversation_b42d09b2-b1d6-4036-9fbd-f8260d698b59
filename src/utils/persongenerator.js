function generateMockData(count,poolId,type) {
    return new Promise((resolve, reject) => {
        const mockData = [];
        const randomInt = new Date().getTime()
       
        for (let i = 0; i < count; i++) {
            mockData.push({
                orgName: `Org ${i + 1}`,
                emailUser: `user${i + 1}`,
                poolId: poolId,
                telephone: `************${i}`,
                email: `email${i + 1}@example.com`,
                poolName: `PoolName${randomInt}`,
                contactId: `id${randomInt + i}`,
                isInner: type
            });
        }
        const num = Math.floor(Math.random() * (3000 - 500 + 1)) + 10
        setTimeout(() => {
            if (count > 200000000) {
                reject(new Error("请求的数据量过大"));
            } else {
                const a = [{
                    "orgName": "Org 1",
                    "emailUser": "user1",
                    "poolId": "sub1709013210",
                    "telephone": "************0",
                    "email": "<EMAIL>",
                    "poolName": "p1",
                    "contactId": "id1711447400588",
                    "isInner": "1"
                },
                    {
                        "orgName": "Org 2",
                        "emailUser": "user2",
                        "poolId": "sub1709013210",
                        "telephone": "************1",
                        "email": "<EMAIL>",
                        "poolName": "p1",
                        "contactId": "id1711447400589",
                        "isInner": "1"
                    },
                    {
                        "orgName": "Org 3",
                        "emailUser": "user3",
                        "poolId": "sub1709013210",
                        "telephone": "************2",
                        "email": "<EMAIL>",
                        "poolName": "p1",
                        "contactId": "id1711447400590",
                        "isInner": "1"
                    },]

                const b = [
                    {
                        "orgName": "Org 1",
                        "emailUser": "user1",
                        "poolId": "sub1706517728",
                        "telephone": "************0",
                        "email": "<EMAIL>",
                        "poolName": "p2",
                        "contactId": "id1711447400588",
                        "isInner": "1"
                    }, {
                    "orgName": "Org 1",
                    "emailUser": "user1-2",
                    "poolId": "sub1706517728",
                    "telephone": "************0",
                    "email": "<EMAIL>",
                        "poolName": "p2",
                    "contactId": "id1711447491424",
                    "isInner": "1"
                },
                    {
                        "orgName": "Org 2",
                        "emailUser": "user2",
                        "poolId": "sub1706517728",
                        "telephone": "************1",
                        "email": "<EMAIL>",
                        "poolName": "2",
                        "contactId": "id1711447491425",
                        "isInner": "1"
                    },]
                // if (poolId === 'sub1709013210') {
                //     resolve(a);
                // } else if (poolId === 'sub1706517728') {
                //     resolve(b);
                // }
                resolve(mockData);
            }
        }, 0);
    });
}

const genId = () => {
    return `id${new Date().getTime()}`
}
export default generateMockData