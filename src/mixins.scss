@mixin scrollbar-track-style {
  /* 滚动条整体样式 */
  &::-webkit-scrollbar {
    width: 8px; /* 滚动条宽度 */
    background-color: #f5f5f5; /* 滚动条背景色 */
  }

  /* 滚动条轨道样式 */
  &::-webkit-scrollbar-track {
    background-color: #f5f5f5; /* 滚动条轨道背景色 */
  }

  /* 滚动条滑块样式 */
  &::-webkit-scrollbar-thumb {
    background-color: #888; /* 滚动条滑块背景色 */
    border-radius: 4px; /* 滚动条滑块圆角 */
  }

  /* 滚动条滑块悬停样式 */
  &::-webkit-scrollbar-thumb:hover {
    background-color: #555; /* 滚动条滑块悬停时的背景色 */
  }

  /* 滚动条按钮样式（上下箭头） */
  &::-webkit-scrollbar-button {
    display: none; /* 隐藏滚动条按钮 */
  }
}
