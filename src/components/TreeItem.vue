<!--eslint-disable-->

<template>
  <li>
    <div class="head" @click="nodeClicked">
      <img
        :class="['triangle', showChildrenLocal ? 'rotate' : '']"
        :src="require('@/assets/arrow.svg')"
      />

      <span style="margin-left: 15px">
        {{ node.orgName }}
      </span>

      <van-radio-group class="radio" v-model="radio" @change="radioChanged">
        <van-radio name="1" shape="square"></van-radio>
      </van-radio-group>
      <!--      <van-button type="info" @click="confirm()" size="mini" plain >选择</van-button>-->
    </div>
    <ul v-if="showChildrenLocal">
      <tree-item
        v-for="child in node.children"
        :expandAll="expandAll"
        :key="child.orgId"
        :node="child"
        @nodeClicked="addChildPath"
      ></tree-item>
    </ul>
  </li>
</template>

<script>
function fetchChildrenMock(orgId) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const children = [
        { orgId: `${orgId}-1`, orgName: "子组织1", children: [] },
        { orgId: `${orgId}-2`, orgName: "子组织2", children: [] },
      ];
      resolve(children);
    }, 1000);
  });
}

export default {
  name: "TreeItem",
  props: {
    // 其他属性
    expandAll: Boolean,
    node: Object,
    show: {
      type: Boolean,
      default: false,
    },
    selectedOrg: String,
    radioName: {
      type: String,
      default: "orgSelection",
    },
  },
  data() {
    return {
      selected: false,
      radio: "",
      showChildrenLocal: this.expandAll,
      path: [],
      agencyName: "",
    };
  },
  watch: {
    expandAll(newValue) {
      this.showChildrenLocal = newValue; // 当expandAll属性改变时更新showChildrenLocal
    },
  },
  computed: {},
  methods: {
    async fetchChildren() {
      if (!this.showChildrenLocal && this.node.children.length === 0) {
        try {
          const children = await fetchChildrenMock(this.node.orgId);
          console.log(children);
          // eslint-disable-next-line vue/no-mutating-props
          this.node.children = children;
        } catch (error) {
          console.error(error);
        }
      }
    },
    radioChanged(event) {
      this.confirm();
      // this.nodeClicked()
      this.$emit("orgSelected", event.target.value);
    },
    confirm(name) {
      setTimeout(() => {
        this.$eventHub.$emit("confirmAgency", () => {});
      }, 100);
      // this.emitPath()
    },
    toggle() {
      if (this.node.children && this.node.children.length > 0) {
        this.toggleChildren();
      }
    },
    async nodeClicked() {
      await this.fetchChildren();
      if (this.node.children && this.node.children.length > 0) {
        this.toggleChildren();
        this.path = [this.node.orgId];
        this.$emit("nodeClicked", [this.node.orgId], this.node.orgName);
      } else {
        this.emitPath([this.node.orgId], this.node.orgName);
      }
    },
    toggleChildren() {
      this.showChildrenLocal = !this.showChildrenLocal;
    },
    addChildPath(childPath, nodeName) {
      this.emitPath([this.node.orgId, ...childPath], nodeName);
    },
    emitPath(path, nodeName) {
      this.path = path;
      this.$emit("nodeClicked", path, nodeName);
    },
  },
};
</script>

<style lang="scss">
li {
  list-style: none;
  margin-left: 10px;
  font-size: 18px;
  line-height: 40px;
  //color: #4762ff;
  .radio {
    margin-left: auto;
    margin-right: 13px;
  }
}

div {
  cursor: pointer;
}

.head {
  display: flex;
  align-items: center;
  img {
    margin-bottom: 5px;
  }

  .triangle {
    margin-top: 5px;
    height: 25px;
    width: 25px;
    transform: rotate(180deg);
    &.rotate {
      transform: rotate(270deg);
    }
  }
}
</style>
