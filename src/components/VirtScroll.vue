
<template>

    <div id="scroll-box-id" ref="scrollBoxRef" class="virtual-scroll-demo" v-show="list.length>0">
        <!-- <button @click="updateVisibleItems">111</button> -->
        <div class="scroll-container" @scroll="handleScroll" ref="virtualItemsContainer">
            <div class="virtual-items-container" :style="{ height: totalHeight + 'px' }">
                <!-- <div style="position: absolute;width: 100%" ref="itemBox">
                    <div class="virtual-item" ref="item" v-for="(item, index) in list" :key="index">
                        {{ item.emailUser }}
                    </div>
                </div> -->
                <div id="scroll-item" style="position: absolute;width: 100%" ref="itemBox">
                    <div v-for="(item, index) in visibleItems" :key="index">
                        <slot :slotProps="item?item:''" :style="{ height: (itemHeight / totalHeight) * 100 + '%' }">

                        </slot>
                    </div>


                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        itemHeight: {

            type: Number,
            default: 50
        },
      list: {
            type: Array,
            default: () => [],
      }  
    },
    data() {
        return {
            // totalItems: 200, // 总项目数
           
            visibleItems: [], // 可见项目
            scrollTop: 0, // 滚动位置
        };
    },
    watch: {
      list() {
        this.updateVisibleItems();
      }  
    },
    computed: {
        totalItems() {
          return this.list.length  
        },
        totalHeight() {
            return this.totalItems * this.itemHeight;
        },
    },
    methods: {
 
        getHt() {
            console.log(this.$refs.item[0].offsetHeight)
        },
        handleScroll(event) {
            this.scrollTop = event.target.scrollTop;
            this.updateVisibleItems();
        },
        updateVisibleItems() {
            console.log(`output->11`,11)
            window.requestAnimationFrame(() => {
                const startIndex = Math.floor(this.scrollTop / this.itemHeight);
                const endIndex = Math.min(
                    Math.ceil((this.scrollTop + this.$el.clientHeight) / this.itemHeight),
                    this.totalItems
                );
                this.visibleItems = [];
                // this.$refs.itemBox.style.top = `${this.scrollTop}px`;
                this.$refs.itemBox.style.top = `${startIndex * this.itemHeight}px`;
                for (let i = startIndex; i < endIndex; i++) {
                    this.visibleItems.push(this.list[i]);
                }
            })

        },
    },
    mounted() {
        this.updateVisibleItems();

       
        

    },
   
    beforeUpdate() {
        var currentElement = this.$refs.scrollBoxRef
        var parentElement = currentElement.parentNode;
        var parentHeight = parentElement.offsetHeight;
        var currentElementInner = currentElement.querySelector('.scroll-container')
        if (currentElementInner.style.height != parentHeight + 'px'&&parentHeight!==0) {
            currentElementInner.style.height = parentHeight + 'px';
            this.updateVisibleItems();
        }

    }
};
</script>

<style scoped>
* {
    box-sizing: border-box;
}
::-webkit-scrollbar {
    display: none;
}
.virtual-scroll-demo {
    height: 100%;
    /* border: 1px solid red; */
    /* width: 300px; */
    /* height: 800px; */
    /* overflow: auto; */
}

.scroll-container {
    height: 100%;
    overflow: scroll;
overscroll-behavior:inherit
}

.virtual-items-container {
    position: relative;
}

.virtual-item {
    height: 50px;
    line-height: 50px;
    /* border-bottom: 1px solid #ddd; */
}
</style>
