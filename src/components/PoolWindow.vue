<template>
    <div>

        <el-dialog title="选择人员" :visible.sync="dialogVisible" width="60%" @close="close">
            selectedPerson{{ selectedPerson.length }}
            localSelectedPerson {{ localSelectedPerson.length }}
            poolId{{ poolId }}
            <div class="search-area">
                <el-cascader :show-all-levels="false" filterable @change="handleChange" v-model="selectedOptions"
                    :options="options" placeholder="请选择订阅池" style="width: 200px;"></el-cascader>
                <el-input v-model="keyword" placeholder="请输入姓名"></el-input>
                <el-button type="primary" @click="search">搜索</el-button>
                <el-button @click="reset">重置</el-button>
            </div>

            <el-table ref="multipleTable" :data="filteredData" style="width: 100%">
                <!-- <el-table-column type="selection"></el-table-column> -->
                <el-table-column width="50" label="">
                    <template slot="header" slot-scope="scope">
                        <el-checkbox v-model="allSelected" @change="handleAllSelect"></el-checkbox>
                    </template>
                    <template slot-scope="scope">
                        <el-checkbox v-model="scope.row.selected" @change="handleSelect(scope.row)"></el-checkbox>
                    </template>
                </el-table-column>
                <el-table-column prop="orgName" label="机构"></el-table-column>
                <el-table-column prop="userName" label="姓名"></el-table-column>
                <el-table-column prop="telephone" label="手机号"></el-table-column>
                <el-table-column prop="email" label="邮箱"></el-table-column>
            </el-table>

            <div class="btn-area">
                <el-button type="primary" @click="confirm">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>




export default {
    props: {
        poolId: {
            type: String,
            default: ''
        },
        selectedPerson: {
            type: Array,
            default: () => []
        },
        visible: {
            type: Boolean,
            default: false
        }
    },


    data() {
        return {
            allSelected: false,
            localSelectedPerson: [],

            dialogVisible: false,
            selectedOptions: [],
            options: [{
                value: '1',
                label: '集团内',
                children: []
            },
            {
                value: '0',
                label: '集团外',
                children: []
            }
            ],
            keyword: '',
            tableData: [],
            multipleSelection: [],
        };
    },
    computed:{

        filteredData() {
             if (this.keyword.trim() === "") {
                return this.tableData;
            }
            const keyword = this.keyword.trim().toLowerCase();
            return this.tableData.filter(
                item =>
                    item.userName.toLowerCase().includes(keyword) ||
                    item.contactId.toLowerCase().includes(keyword)
            );
          
        }

    },
    watch: {
        selectedOptions() {
            // console.log(`selectedOptions22`, this.selectedOptions)
        },
        visible(val) {

            this.getPoolList()
            if (this.poolId) {
                const group = this.options.find(group => group.children.some(child => child.value === this.poolId))
                this.selectedOptions = [group.value, this.poolId]
            }
            this.dialogVisible = val
            if (val) {
                this.localSelectedPerson = JSON.parse(JSON.stringify(this.selectedPerson))
            }
        }
    },
    methods: {
        getPoolList() {
            if (this.options[0].children.length > 0) return
            const pools = [{ poolname: '订阅1', poolid: 'dingyue1', isInner: '1' }, { poolname: '订阅2', poolid: 'dingyue2', isInner: '1' }, { poolname: '订阅3', poolid: 'dingyue3', isInner: '0' }]
          
            pools.forEach(pool => {
                const { poolname, poolid, isInner } = pool;
                const option = {
                    value: poolid,
                    label: poolname
                };

                if (isInner === '1') {
                    this.options[0].children.push(option);
                } else {
                    this.options[1].children.push(option);
                }
            });
        },
        handleAllSelect(value) {
            this.tableData.forEach(item => item.selected = value)
            if (value) {
                this.localSelectedPerson.push(...this.tableData.filter(item => item.selected).map(item => Object.assign({}, item)))
            } else {
                this.localSelectedPerson = this.localSelectedPerson.filter(item => !this.tableData.find(selectedItem => selectedItem.contactId === item.contactId))
            }
        },
        handleSelect(obj) {
            // if (obj.selected === false) {
            //     this.localSelectedPerson = this.localSelectedPerson.filter(item => item.contactId !== obj.contactId);
            // } else {
            //     this.localSelectedPerson.push(obj);
            // }
            this.checkAllSelect()
        },
        close() {
            this.dialogVisible = false
            this.$emit('update:visible', false)
        },



        handleChange(value) {

            const id = value[value.length - 1];
            console.log(`切换分组id`, id)
            let resData = mockData(id)
            console.log(`output->resData`, resData)
            // return
            
            let data = resData.map(item => {
                return { ...item, selected: false }
            });

            const poolId = data[0].poolId

            const selectedcontactIdSet = new Set(this.localSelectedPerson.filter(item => item.poolId === poolId).map(item => item.contactId));
            data.forEach(itemA => itemA.selected = selectedcontactIdSet.has(itemA.contactId));
            this.tableData = data
            this.checkAllSelect()



        },
        checkAllSelect() {
            const allSelected = this.tableData.every(item => item.selected === true);
            this.allSelected = allSelected
        },
        search() {
            // 实现搜索逻辑
        },
        reset() {
            this.selectedOptions = [];
            this.keyword = '';
            // 重置搜索条件
        },
        confirm() {
            this.$emit('updatePerson', this.tableData);
            this.close()

        }
    }
};


const mockData = (id) => {
    console.log(`output->iiidd`, id)
    if(id == 'dingyue2') {
        return [
            { poolName: '订阅2', poolId: 'dingyue2', orgName: '机构A', userName: '张三', telephone: '1234567890', email: '<EMAIL>', contactId: '1' },
            { poolName: '订阅2', poolId: 'dingyue2', orgName: '机构B', userName: '李四', telephone: '0987654321', email: '<EMAIL>', contactId: '2' },
            { poolName: '订阅2', poolId: 'dingyue2', orgName: '机构C', userName: '王五', telephone: '1357924680', email: '<EMAIL>', contactId: '3' },
            { poolName: '订阅2', poolId: 'dingyue2', orgName: '机构D', userName: '赵六', telephone: '2468013579', email: '<EMAIL>', contactId: '4' },
        ]
    } else if (id == 'dingyue1') {
        return [
            { poolName: '订阅1', poolId: 'dingyue1', orgName: '机构A', userName: '张三', telephone: '1234567890', email: '<EMAIL>', contactId: '1' },

            { poolName: '订阅1', poolId: 'dingyue1', orgName: '机构E', userName: '小明', telephone: '9876543210', email: '<EMAIL>', contactId: '5' },
            { poolName: '订阅1', poolId: 'dingyue1', orgName: '机构F', userName: '小红', telephone: '0123456789', email: '<EMAIL>', contactId: '6' },

        ]
    } else {
        return []
    }
   
}


</script>

<style lang="scss">
.search-area {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .el-cascader {
        margin-right: 10px;
    }

    .el-input {
        margin-right: 10px;
        width: 200px;
    }

    .el-button {
        margin-right: 10px;
    }
}

.btn-area {
    margin-top: 20px;
    text-align: center;

    .el-button {
        margin-right: 10px;
    }
}
</style>
