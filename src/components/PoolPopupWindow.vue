<template>
    <van-popup class="hide-tab" :lazy-render="false" v-model="popupVisible" position="right"
        :style="{ height: '100%', width: '100%' }">

        <van-tabs v-model="active" :lazy-render="false">
            <van-tab :class="setStyle" title="集团内" v-if="tabVisible('1')">
                <PoolPopupMain ref="poolRef1" :visible="visible" isInner="1"
                    :selectedPoolGroupData="selectedPoolGroupData" :poolList="innerList"
                    :personList.sync="localPersonList" />
            </van-tab>
            <van-tab :class="setStyle" title="集团外" v-if="tabVisible('0')">
                <PoolPopupMain ref="poolRef2" :visible="visible" isInner="0"
                    :selectedPoolGroupData="selectedPoolGroupData" :poolList="outerList"
                    :personList.sync="localPersonList" />
            </van-tab>

        </van-tabs>

        <van-button style="position: fixed; bottom: 20" @click="confirmSelect">confirm</van-button>
        <van-button style="position: fixed; bottom: 20;right: 0;" @click="$emit('closeWindow')">close</van-button>
    </van-popup>
</template>

<script>
import PoolPopupMain from './PoolPopupMain.vue'
export default {
    components: {
        PoolPopupMain
    },
    name: 'PoolPopupView',
    props: {
        selectedPoolGroupData: {
            type: Array,
            default: () => []
        },
        personList: {
            type: Array,
            default: () => []
        },
        visible: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        setStyle() {
            return 'status-view'
        }
    },
    watch: {

        selectedPoolGroupData: {
            handler(val, oldVal) {
                if (val !== oldVal) {
                    if (this.selectedPoolGroupData.length > 0) {

                    }
                }
            },
            deep: true,


        },
        personList: {
            handler(val, oldVal) {
                if (val && oldVal === undefined) {
                    this.setLocalPerson()


                }
            },
            deep: true,
            immediate: true
        },
        visible(val) {
            this.popupVisible = val
            this.setLocalPerson()
        }
    },
    data() {
        return {
            localPersonList: [],
            popupVisible: false,
            active: 0,
            innerList: [

            ],
            outerList: [

            ]
        };
    },
    created() {
        const refactor = (data) => {
            return data.map(({ poolId, poolName, ...rest }) => {
                return {
                    id: poolId,
                    name: poolName,
                    isShow: false,
                    status: false,
                    children: [],
                    allChecked: false,
                    ...rest
                }
            }
            );
        }
        this.innerList = refactor(mock_data.filter(item => item.isInner === '1'))
        this.outerList = refactor(mock_data.filter(item => item.isInner === '0'))

    },
    mounted() {
        console.log(`output->windowmounted`,)

    },
    methods: {
        setLocalPerson() {
            this.localPersonList = JSON.parse(JSON.stringify(this.personList))
        },
        async confirmSelect() {
            const ref1 = this.$refs.poolRef1 ? this.$refs.poolRef1.poolList : []
            const ref2 = this.$refs.poolRef2 ? this.$refs.poolRef2.poolList : []

             const poolList = [...ref1, ...ref2]
           
            const promises = poolList.map(async item => {
                if (item.children && item.children.length == 0 && item.allChecked) {
                    
                    if(item.isInner==='1'){
                        const res = await this.$refs.poolRef1.clickPool(item, false)
                        return res
                    } else {
                        const res = await this.$refs.poolRef2.clickPool(item, false)
                        return res
                    }
                   
                }
            })

            await Promise.all(promises)
         
            console.log(`output->poolList`, poolList)

            const workerCode = `
        self.addEventListener('message', function (e) {
    const poolList = e.data.poolList;
    const localPersonList = e.data.localPersonList;

    poolList.forEach(pool => {
        if (pool.children && pool.children.length > 0) {
            pool.children.forEach(itemA => {
                const isInB = localPersonList.some(itemB => itemB.contactId === itemA.contactId&&itemB.poolId===itemA.poolId);
                if (itemA.selected && !isInB) {
                    localPersonList.push(itemA);
                } else if (!itemA.selected && isInB) {
                    const index = localPersonList.findIndex(itemB => itemB.contactId === itemA.contactId);
                    if (index > -1) {
                        localPersonList.splice(index, 1);
                    }
                }
            });
        }
    });

    self.postMessage(localPersonList);
});

      `;

            const blob = new Blob([workerCode], { type: 'application/javascript' });
            const workerURL = URL.createObjectURL(blob);
            const worker = new Worker(workerURL);




            // const worker = new Worker('worker.js');

            const localPersonList = this.localPersonList

            worker.postMessage({ poolList, localPersonList });

            worker.addEventListener('message', (e) => {
                const updatedLocalPersonList = e.data;
                // 处理从Web Worker返回的数据
                this.$emit('update:personList', updatedLocalPersonList)
                this.$emit('closeWindow')


            });

            URL.revokeObjectURL(workerURL);





        },
        tabVisible(type) {
            if (this.selectedPoolGroupData.length > 0) {
                return this.selectedPoolGroupData[0]?.isInner === type;
            } else {
                return true
            }

        },
        deletePool(value) {
          
            const poolId = value[0].poolId

            this.localPersonList = this.localPersonList.filter(item => item.poolId !== poolId);
            this.$emit('update:personList', this.localPersonList)

        },

    }
};

const mock_data = [
    {
        "poolId": "sub1709013210",
        "poolName": "订阅产品长度测试",
        "isInner": "1"
    },
    {
        "poolId": "sub1706517728",
        "poolName": "客户信息",
        "isInner": "1"
    },
    {
        "poolId": "sub1703757504",
        "poolName": "1228002",
        "isInner": "1"
    },
    {
        "poolId": "sub1703751227",
        "poolName": "订阅1228001",
        "isInner": "1"
    },
    {
        "poolId": "sub1703750341",
        "poolName": "订阅1228",
        "isInner": "1"
    },
    {
        "poolId": "sub1702867026",
        "poolName": "法国红酒",
        "isInner": "1"
    },
    {
        "poolId": "sub1702447724",
        "poolName": "全部指数产品",
        "isInner": "1"
    },
    {
        "poolId": "sub1702437219",
        "poolName": "金融松紧指数",
        "isInner": "1"
    },
    {
        "poolId": "sub1702436499",
        "poolName": "全球风险偏好指数",
        "isInner": "1"
    },
    {
        "poolId": "sub1702431641",
        "poolName": "美国劳动力市场供需指数",
        "isInner": "1"
    },
    {
        "poolId": "sub1702284507",
        "poolName": "订阅池-1211002",
        "isInner": "1"
    },
    {
        "poolId": "sub1702283740",
        "poolName": "订阅池-12110001",
        "isInner": "1"
    },
    {
        "poolId": "sub1702282569",
        "poolName": "订阅池-负面解读",
        "isInner": "1"
    },
    {
        "poolId": "sub1702281733",
        "poolName": "订阅池1211",
        "isInner": "1"
    },
    {
        "poolId": "sub1699603963",
        "poolName": "订阅111001",
        "isInner": "1"
    },
    {
        "poolId": "sub1699524486",
        "poolName": "567",
        "isInner": "1"
    },
    {
        "poolId": "sub1699524390",
        "poolName": "45566",
        "isInner": "1"
    },
    {
        "poolId": "sub1699524063",
        "poolName": "订阅池1109005",
        "isInner": "1"
    },
    {
        "poolId": "sub1699513394",
        "poolName": "总行订阅池",
        "isInner": "1"
    },
    {
        "poolId": "sub1699511619",
        "poolName": "订阅池1109004",
        "isInner": "1"
    },
    {
        "poolId": "sub1699509329",
        "poolName": "订阅池1109003",
        "isInner": "1"
    },
    {
        "poolId": "sub1699499026",
        "poolName": "订阅池1109-1",
        "isInner": "1"
    },
    {
        "poolId": "sub1699494159",
        "poolName": "订阅池1109",
        "isInner": "1"
    },
    {
        "poolId": "sub1699424731",
        "poolName": "订阅池110802",
        "isInner": "1"
    },
    {
        "poolId": "sub1699423413",
        "poolName": "订阅池1108",
        "isInner": "1"
    },
    {
        "poolId": "sub1699327263",
        "poolName": "订阅池1107",
        "isInner": "1"
    },
    {
        "poolId": "sub1698141537",
        "poolName": "研报周度精选",
        "isInner": "1"
    },
    {
        "poolId": "sub1698042307",
        "poolName": "模型产品订阅",
        "isInner": "1"
    },
    {
        "poolId": "sub1697161645",
        "poolName": "测试订阅合同1013",
        "isInner": "1"
    },
    {
        "poolId": "sub1694432473",
        "poolName": "行务渠道研报权限查看",
        "isInner": "1"
    },
    {
        "poolId": "sub1693986424",
        "poolName": "我的新增03",
        "isInner": "1"
    },
    {
        "poolId": "sub1693817387",
        "poolName": "我的新增02",
        "isInner": "1"
    },
    {
        "poolId": "sub1693817065",
        "poolName": "我的新增",
        "isInner": "1"
    },
    {
        "poolId": "sub1693461903",
        "poolName": "订阅池测试",
        "isInner": "1"
    },
    {
        "poolId": "sub1691568424",
        "poolName": "订阅池",
        "isInner": "1"
    },
    {
        "poolId": "sub1691485302",
        "poolName": "发起订阅里修改订阅池名称--22名字很长该怎么展示",
        "isInner": "1"
    },
    {
        "poolId": "sub1691460881",
        "poolName": "订阅池0808修改01",
        "isInner": "1"
    },
    {
        "poolId": "sub1690544095",
        "poolName": "测试修改订阅池名修改",
        "isInner": "1"
    },
    {
        "poolId": "sub1690277888",
        "poolName": "定制化订阅池",
        "isInner": "1"
    },
    {
        "poolId": "contra-20230613-0003",
        "poolName": "0613hh",
        "isInner": "0"
    },
    {
        "poolId": "contra-20230608-0005",
        "poolName": "UI哦",
        "isInner": "0"
    },
    {
        "poolId": "contra-1687399664394",
        "poolName": "0622",
        "isInner": "0"
    },
    {
        "poolId": "contra-1689067665837",
        "poolName": "非定向验证711",
        "isInner": "0"
    },
    {
        "poolId": "contra-1689069152555",
        "poolName": "验证非定向邮件711金融",
        "isInner": "0"
    },
    {
        "poolId": "contra-1688112642302",
        "poolName": "630结束合同",
        "isInner": "0"
    },
    {
        "poolId": "contra-1688111672254",
        "poolName": "到期合同",
        "isInner": "0"
    },
    {
        "poolId": "contract-0012",
        "poolName": "正式合同0012",
        "isInner": "0"
    },
    {
        "poolId": "234567",
        "poolName": "查看APP创建人",
        "isInner": "0"
    },
    {
        "poolId": "沪红头文字--htt",
        "poolName": "fuce2",
        "isInner": "0"
    },
    {
        "poolId": "contra-1690353887178",
        "poolName": "测试合同审批",
        "isInner": "0"
    },
    {
        "poolId": "contra-1690533294108",
        "poolName": "测试合同同步",
        "isInner": "0"
    },
    {
        "poolId": "contra-1690533188270",
        "poolName": "测试合同审批状态同步",
        "isInner": "0"
    },
    {
        "poolId": "1235678",
        "poolName": "非定向FICC系列产品验证",
        "isInner": "0"
    },
    {
        "poolId": "c-123456",
        "poolName": "测试0810-2",
        "isInner": "0"
    },
    {
        "poolId": "23456786543",
        "poolName": "全产品非定向验证",
        "isInner": "0"
    },
    {
        "poolId": "contra-1689061206168",
        "poolName": "非定向验证对不对",
        "isInner": "0"
    },
    {
        "poolId": "45678976543",
        "poolName": "0818合同",
        "isInner": "0"
    },
    {
        "poolId": "202309081647",
        "poolName": "消费中心合同1",
        "isInner": "0"
    },
    {
        "poolId": "contra-1697425513296",
        "poolName": "我的合同1016",
        "isInner": "0"
    },
    {
        "poolId": "contra-1694774568065",
        "poolName": "7月合同",
        "isInner": "0"
    },
    {
        "poolId": "202311241438",
        "poolName": "pdf文件下载",
        "isInner": "0"
    },
    {
        "poolId": "202315381124",
        "poolName": "全看研报",
        "isInner": "0"
    },
    {
        "poolId": "202311291447",
        "poolName": "生产中心的账号",
        "isInner": "0"
    },
    {
        "poolId": "202401091643",
        "poolName": "3-5级研报查看",
        "isInner": "0"
    },
    {
        "poolId": "202401151637",
        "poolName": "附件查看PDF",
        "isInner": "0"
    },
    {
        "poolId": "202401231109",
        "poolName": "查看合同",
        "isInner": "0"
    },
    {
        "poolId": "202402021433",
        "poolName": "2024年的第一份合同",
        "isInner": "0"
    },
    {
        "poolId": "202403051602",
        "poolName": "合同到期检查4",
        "isInner": "0"
    },
    {
        "poolId": "202403061645",
        "poolName": "绿金监管-5",
        "isInner": "0"
    }
]
</script>
<style lang="less" scoped>
.hide-tab {
    ::v-deep .van-tabs__wrap {
        height: 0;
    }
}

.status-view {

    // border: 1px solid red;
    height: calc(~'100vh - 105px');
    overflow: scroll;
}
</style>
