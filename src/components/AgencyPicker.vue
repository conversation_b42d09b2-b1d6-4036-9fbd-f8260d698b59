<!--eslint-disable-->
<template>
 <div>
   <button @click="expandAllNodes">展开所有节点</button>

   <div class="box">
     <ul>
       <van-search
           v-model="searchKeyword"
           @input="filterTree"
           placeholder="请输入机构名"
       />
       <tree-item
           :expandAll="expandAll"
           v-for="item in filteredTreeData"
           :key="item.orgId"
           :node="item"
           @orgSelected="handleOrgSelected"
           @nodeClicked="handleNodeClicked"
       ></tree-item>
     </ul>
   </div>
 </div>
<!--  <van-popup-->
<!--    v-model="show"-->
<!--    position="right"-->
<!--    :style="{ width: '100%', height: '100%' }"-->
<!--  >-->
<!--   -->
<!--  </van-popup>-->
</template>

<script>
// import { reqParam } from "../../../utils/reqParam";
import TreeItem from "./TreeItem";

export default {
  components: {
    TreeItem,
  },
  props: {
    type: {
      type: String,
      default: "",
    },
    show: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    name() {
      if (this.type === "") {
        return "";
      } else if (this.type === "visit") {
        return "拜访";
      } else if (this.type === "research") {
        return "调研";
      } else if (this.type === "train") {
        return "路演培训";
      } else {
        return "";
      }
    },
  },
  watch: {
    searchKeyword(val) {
      if (val) {
        this.expandAll = true;
      } else {
        this.expandAll = false;
      }
    },
  },
  data() {
    return {
      expandAll: false,
      showAllChildren: true,
      selectedOrg: null,
      node: [],
      searchKeyword: "",
      filteredTreeData: [],
      treeData: [],
    };
  },
  methods: {
    expandAllNodes() {
      this.expandAll = true;
    },
    handleOrgSelected(orgId) {
      this.selectedOrg = orgId; // 更新选中的组织ID
    },
    back() {
      this.$emit("update:show", false);
    },
    handleNodeClicked(path, name) {
      console.log(path, name);
      this.node = path;

      this.$emit("confirm", path, name);
    },
    filterTree() {
      if (this.searchKeyword.trim() === "") {
        this.filteredTreeData = this.treeData;
        return;
      }
      setTimeout(() => {
        // _.debounce(func, [wait=0])
        this.filteredTreeData = [
          {
            children: [
              {
                children: [],
                orgId: "cust-1682045431",
                orgName: "上海分行",
                parentId: "cust-1682044140",
                parentName: "上海银行",
              },
            ],
            orgId: "cust-1682044140",
            orgName: "上海银行",
            parentId: "-1",
          },
          {
            children: [
              {
                children: [
                  {
                    children: [],
                    orgId: "cust-1682243056",
                    orgName: "川沙分行",
                    parentId: "cust-1682045999",
                    parentName: "上海分行2",
                  },
                ],
                orgId: "cust-1682045999",
                orgName: "上海分行2",
                parentId: "cust-1682045497",
                parentName: "宁坡银行",
              },
              {
                children: [],
                orgId: "cust-1682045588",
                orgName: "宁坡分行",
                parentId: "cust-1682045497",
                parentName: "宁坡银行",
              },
              {
                children: [],
                orgId: "cust-5b2697a2-5cff-4550-8976-ba156cd70a91",
                orgName: "宁波银行总部",
                parentId: "cust-1682045497",
                parentName: "宁坡银行",
              },
            ],
            orgId: "cust-1682045497",
            orgName: "宁坡银行",
            parentId: "-1",
          },
        ];
      }, 500);
      const filterNodes = (nodes) => {
        return nodes
          .map((node) => {
            if (node.orgName.includes(this.searchKeyword)) {
              return node;
            }
            if (node.children && node.children.length > 0) {
              const children = filterNodes(node.children);
              if (children.length > 0) {
                return { ...node, children };
              }
            }
            return null;
          })
          .filter((node) => node !== null);
      };

      this.filteredTreeData = filterNodes(this.treeData);
    },
  },
  mounted() {
    this.treeData = [
      {
        children: [
          {
            children: [
              {
                children: [],
                orgId: "cust-1681993577",
                orgName: "夏洛特",
                parentId: "cust-1681800475",
                parentName: "上单",
              },
              {
                children: [],
                orgId: "cust-3d5d4ff1-430d-44e7-95bd-ad8aa4d3e538",
                orgName: "吕布",
                parentId: "cust-1681800475",
                parentName: "上单",
              },
            ],
            orgId: "cust-1681800475",
            orgName: "上单",
            parentId: "cust-1681696048",
            parentName: "王者荣耀",
          },
        ],
        orgId: "cust-1681696048",
        orgName: "王者荣耀",
        parentId: "-1",
      },
    ];

    function generateMockData(count) {
      const mockData = [];

      for (let i = 0; i < count; i++) {
        const item = {
          children: [],
          orgId: `cust-${Math.floor(Math.random() * 1000000000)}`,
          orgName: "王者荣耀",
          parentId: "-1",
        };

        mockData.push(item);
      }

      return mockData;
    }

    const numberOfItems = 500;
    const mockData = generateMockData(numberOfItems);
    this.filteredTreeData =mockData

  },
};
</script>

<style scoped lang="scss">
.box {
  //border: 1px solid red;
  height: calc(100vh - 100px);
  overflow: scroll;
}
</style>
