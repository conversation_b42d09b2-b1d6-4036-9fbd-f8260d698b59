<template>
    <div>


        <van-search enterkeyhint="search" v-model="keyword" v-show="!hasSelectedGroup" placeholder="请输入机构名或姓名" />
        <div class="option">

            <div v-for="item in poolListComputed" :key="item.id" v-show="poolVisible(item)">
                <div class="deptItem" @click.stop="clickPool(item)">
                    <div style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap">
                        <van-icon v-if="!item.isShow" name="arrow" />
                        <van-icon v-else name="arrow-down" />
                        {{ item.name }}
                    </div>

                    <div class="rightIcon" @click.stop="handlePoolSelect(item)">
                        <div v-if="isAllChek(item.children) || item.allChecked">
                            <img :src="require('@/assets/right.png')" alt="" class="icon" />
                        </div>

                        <div v-else>
                            <img :src="require('@/assets/box.png')" alt="" class="icon" />
                        </div>
                    </div>
                </div>

                <div class="month" v-show="item.isShow">
                    <!-- <VirtScroll :list="item.children" /> -->
                    <div class="scroll-box" v-show="item.isShow">
                        <!-- <span> 2 </span> -->
                        <VirtScroll style="" v-if="item.children.length > 0" :list="item.children" :item-height="50">
                            <template v-slot="{ slotProps }">
                                <!-- <div class="custom-virtual-item" :style="{ height: '100%' }">
                                <div class="item-content">
                                    {{ emailUser }}
                                </div>
                            </div> -->

                                <div class="personItem" @click="handleSelect(item, slotProps)">
                                    <div>{{ slotProps.emailUser }}</div>
                                    <!-- {{ slotProps.selected }} -->
                                    <div v-if="slotProps.selected">
                                        <img :src="require('@/assets/right.png')" alt="" class="icon" />
                                    </div>
                                </div>
                            </template>
                        </VirtScroll>
                    </div>

                    <!-- <div v-for="one in filterPerson(item.children)" :key="one.contactId" class="personItem"
                        @click="handleSelect(item, one)">
                        <div>{{ one.emailUser }}</div>
                        <div v-if="one.selected">
                            <img :src="require('@/assets/right.png')" alt="" class="icon" />
                        </div>
                    </div> -->
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import VirtScroll from '@/components/VirtScroll.vue'
import virtualScrollView from '@/views/old/virtualScrollView.vue';
import persongenerator from '@/utils/persongenerator'
export default {
    components: {
        VirtScroll,
        virtualScrollView
    },
    name: 'PoolPopupView',
    props: {

        visible: {
            type: Boolean,
            default: false
        },
        selectedPoolGroupData: {
            type: Array,
            default: () => []
        },
        personList: {
            type: Array,
            default: () => []
        },
        poolList: {
            type: Array,
            default: () => []
        }
    },
    created() {

    },
    watch: {
        selectedPoolGroupData: {
            handler(val, oldVal) {

                if (val) {

                    if (this.selectedPoolGroupData.length > 0) {

                        const poolId = this.selectedPoolGroupData[0].poolId
                        console.log(`output->获取`, poolId)
                        const item = this.poolList.find(item => item.id === poolId)
                        if (item) {
                            console.log(`output->找到`, item)
                            this.clickPool(item, true)
                        }
                    }
                }

            },
            deep: true,
            immediate: true

        },
        visible: {
            handler(val) {
                if (val) {
                    this.setLocalPerson()
                    this.toggleSelect()
                }
            },
            immediate: true

        },

        localSelectedPerson(val) {

            this.$emit('update:personList', val)
        }
    },
    computed: {
        hasSelectedGroup() {
            return this.selectedPoolGroupData.length > 0
        },
        localPersonIds() {
            // this.localSelectedPerson.some(item2 => item2.contactId === item.contactId)  
            return this.localSelectedPerson.map(item => item.contactId)
        },
        poolListComputed() {

            if (this.keyword.trim() === "") {
                return this.poolList;
            }
            const keyword = this.keyword.trim().toLowerCase();
            return this.poolList.filter(
                item =>
                    item.name.toLowerCase().includes(keyword)
            );
        }
    },
    data() {
        return {
            localSelectedPerson: [],
            showDeptOption: true,
            keyword: '',
            deptPersonList: [
            ]
        };
    },
    methods: {
        filterPerson(list) {
            return list
            if (this.keyword.trim() === "") {
                return list;
            }
            const keyword = this.keyword.trim().toLowerCase();
            return list.filter(
                item =>
                    item.emailUser.toLowerCase().includes(keyword)
            );
        },
        toggleSelect() {
            // 首先，将idList转换为Set，以提高查找效率
            const idSet = new Set(this.personList.map(item => item.contactId));

            // 然后，和之前一样遍历poolList中的elements
            const elements = this.poolList.filter(item => item.children.length > 0);
            if (elements.length > 0) {
                elements.forEach(element => {
                    element.children.forEach(child => {
                        // 使用Set来检查是否包含child.contactId
                        child.selected = idSet.has(child.contactId);
                    });
                });
            }

        },
        setLocalPerson() {
            this.localSelectedPerson = this.personList
        },
        poolVisible(item) {
            const poolData = this.selectedPoolGroupData
            if (poolData.length > 0 && item.id !== poolData[0].poolId) {
                return false
            } else {
                return true
            }
        },
        isAllChek(list) {
            if (list.length === 0) return false

            return list.every(item => item.selected === true)
        },
        clickPool(item, defaultOpen = '') {
            return new Promise((resolve) => {
                if (defaultOpen === '') {
                    item.isShow = !item.isShow
                } else {
                    item.isShow = defaultOpen
                }

                if (item.children.length === 0) {
                    // const num = Math.floor(Math.random() * (50 - 10 + 1)) + 10;
                    const num = 20000;
                    persongenerator(num, item.id, item.isInner).then(list => {

                        let data = list.map(item => {
                            return { ...item, selected: false }
                        });

                        const poolId = data[0].poolId

                        const selectedcontactIdSet = new Set(this.localSelectedPerson.filter(item => item.poolId === poolId).map(item => item.contactId));
                        data.forEach(itemA => itemA.selected = selectedcontactIdSet.has(itemA.contactId) || item.allChecked);

                        item.allChecked = false
                        item.children = data

                        resolve()
                    }).catch(error => {
                        console.error("发生错误:", error);
                    });




                }
            })

        },
        handlePoolSelect(item) {
            const doSelect = () => {
                const isAllChek = this.isAllChek(item.children)
                if (isAllChek) {
                    item.allChecked = false
                }
                item.children.forEach(item => {
                    item.selected = !isAllChek

                })
            }
            if (item.children.length > 0) {
                doSelect()

            } else {
                item.allChecked = !item.allChecked
                // this.clickPool(item, false).then(() => {
                //     doSelect()
                // })
            }

        },
        handleSelect(dept, person) {
            // const id = person.contactId
            // dept.children.forEach(item => {
            //     if (id === item.contactId) {
            //         item.selected = !item.selected
            //     }
            // })
            // console.log(`output->person`, person)
            person.selected = !person.selected
            // this.handlePerson(person)
        },
        handlePerson(person) {
            if (person.selected === false) {
                this.localSelectedPerson = this.localSelectedPerson.filter(item => item.contactId !== person.contactId);
            } else {
                const existingPerson = this.localSelectedPerson.find(p => p.contactId === person.contactId);

                if (!existingPerson) {
                    this.localSelectedPerson.push(person);
                }

            }
        },


        onSearchTree() {
            // Method when searching in the tree
        },
        onCancelTree() {
            // Method to cancel the search in the tree
        },
        clickAllCompany() {
            // Method when clicking on the All Company option
        },


    }
};

</script>
<style lang="less" scoped>
.scroll-box {
    // min-height: 50px;

    // height: 200px;

    max-height: 200px;

    overflow: hidden;
}

.searchFor {
    background: #ffffff;
    position: fixed;
    top: 92px;
    left: 0;
    color: #1d2341;
    z-index: 10000;
    width: 95%;
    padding: 0 2.5% 10px 2.5%;

    .searchLine {
        overflow: hidden;
        height: 48px;
        line-height: 48px;
        border-bottom: 1px solid #eeeeee;
        position: relative;

        .searchTitle {
            float: left;
        }

        .searchStatus {
            /deep/ .van-dropdown-menu {
                float: right;
                margin-right: 5px;

                .van-dropdown-menu__bar {
                    box-shadow: none;
                    height: auto;
                }

                .van-dropdown-item__content {
                    width: 100%;
                }

                .van-popup--top {
                    left: auto;
                    right: 0;
                }
            }

            /deep/ .van-cell {
                float: right;
                font-size: inherit;
                padding: 0px 16px;
                line-height: 38px;
            }

            /deep/ .van-dropdown-menu__title {
                font-size: inherit;
                line-height: inherit;
                color: #1d2341;
            }
        }

        .searchTime {
            /deep/ .van-cell {
                float: right;
                padding: 0;
                height: 48px;
                line-height: 48px;
                color: #1d2341;
                font-size: inherit;

                .van-cell__value {
                    color: #1d2341;
                }
            }
        }

        .searchName {
            /deep/ .van-cell {
                float: right;
                padding: 0;
                height: 48px;
                line-height: 48px;
                font-size: inherit;

                .van-cell__right-icon {
                    line-height: 48px !important;
                }
            }

            .van-cell::after {
                display: none;
            }

            /deep/ .van-field__label {
                color: #1d2341;
            }

            /deep/ .van-field__control {
                text-align: right;
                color: #1d2341;
            }
        }

        img {
            position: absolute;
            top: 17px;
            right: 0;
        }
    }

    .searchBtn {
        width: 220px;
        margin: 0 auto;
        margin-top: 10px;

        .van-button {
            width: 70px;
            height: 30px;
            line-height: 30px;
            border-radius: 10px;
        }

        .van-button--default {
            margin-right: 70px;
            border-radius: 10px;
            border: 1px solid rgb(60, 108, 254);
            color: rgb(60, 108, 254);
        }
    }
}


.option {
    padding: 0 16px;
    border-top: 1px solid #e9e9e9;
    height: calc(~'100vh - 176px');
    overflow: scroll;

    // fsf 
    .optionItem {
        display: flex;
        justify-content: space-between;
        height: 50px;
        line-height: 50px;
        border-bottom: 1px solid #e9e9e9;
    }

    .monthItem {

        display: flex;
        justify-content: space-between;
        height: 50px;
        line-height: 50px;
        border-bottom: 1px solid #e9e9e9;
        padding-left: 16px;
    }

    .deptItem {


        z-index: 999;
        display: flex;
        justify-content: space-between;
        height: 50px;
        line-height: 50px;
        border-bottom: 1px solid #e9e9e9;
        // padding-left: 16px;
    }

    .deptItem1 {
        display: flex;
        justify-content: space-between;
        height: 50px;
        line-height: 50px;
        border-bottom: 1px solid #e9e9e9;
        // padding-left: 16px;
    }

    .personItem {
        display: flex;
        justify-content: space-between;
        height: 50px;
        line-height: 50px;
        border-bottom: 1px solid #e9e9e9;
        padding-left: 36px;
    }
}

.oneCell {
    display: flex;
    justify-content: flex-end;
    line-height: 50px;
    height: 50px;
    // text-align: left;
}

.search-icon {
    font-size: 16px;
    line-height: inherit;
}

/deep/.van-search__content {
    border-radius: 8px;
}

.icon {
    width: 20px;
    height: 20px;
    display: block;

    position: relative;
    top: 25px;
    transform: translate(0, -50%);
}

svg {
    width: 20px;
    margin-right: 5px;
}

.rightIcon {
    // border: 1px solid red;
    width: 30px;
    height: auto;
    display: flex;
    justify-content: flex-end;
}
</style>
