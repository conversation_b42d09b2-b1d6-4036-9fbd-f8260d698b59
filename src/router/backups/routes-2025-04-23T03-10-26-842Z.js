const HomeView = () =>
  import(/* webpackChunkName: "HomeView" */ "../views/HomeView.vue");

const hoverInfoView = () =>
  import(/* webpackChunkName: "hoverInfoView" */ "../views/new/hoverInfoView.vue");

const ApprovalLIstView = () =>
  import(/* webpackChunkName: "ApprovalLIstView" */ "../views/old/ApprovalLIstView.vue");

const buttonView = () =>
  import(/* webpackChunkName: "buttonView" */ "../views/old/buttonView.vue");

const customButtonView = () =>
  import(/* webpackChunkName: "customButtonView" */ "../views/old/customButtonView.vue");

const customCascaderView = () =>
  import(/* webpackChunkName: "customCascaderView" */ "../views/old/customCascaderView.vue");

const DayDiffView = () =>
  import(/* webpackChunkName: "DayDiffView" */ "../views/old/DayDiffView.vue");

const disableiosRubberBandEffectDemoView = () =>
  import(/* webpackChunkName: "disableiosRubberBandEffectDemoView" */ "../views/old/disableiosRubberBandEffectDemoView.vue");

const ElemTableValidateView = () =>
  import(/* webpackChunkName: "ElemTableValidateView" */ "../views/old/ElemTableValidateView.vue");

const ELForm1View = () =>
  import(/* webpackChunkName: "ELForm1View" */ "../views/old/ELForm1View.vue");

const ELForm2View = () =>
  import(/* webpackChunkName: "ELForm2View" */ "../views/old/ELForm2View.vue");

const ElForm3View = () =>
  import(/* webpackChunkName: "ElForm3View" */ "../views/old/ElForm3View.vue");

const ElMessageView = () =>
  import(/* webpackChunkName: "ElMessageView" */ "../views/old/ElMessageView.vue");

const FileDemoView = () =>
  import(/* webpackChunkName: "FileDemoView" */ "../views/old/FileDemoView.vue");

const FileNameDemoView = () =>
  import(/* webpackChunkName: "FileNameDemoView" */ "../views/old/FileNameDemoView.vue");

const FormDemo1View = () =>
  import(/* webpackChunkName: "FormDemo1View" */ "../views/old/FormDemo1View.vue");

const FormDisabledStyleView = () =>
  import(/* webpackChunkName: "FormDisabledStyleView" */ "../views/old/FormDisabledStyleView.vue");

const formValidateView = () =>
  import(/* webpackChunkName: "formValidateView" */ "../views/old/formValidateView.vue");

const handwrittenTreeView = () =>
  import(/* webpackChunkName: "handwrittenTreeView" */ "../views/old/handwrittenTreeView.vue");

const newAddressSelectorView = () =>
  import(/* webpackChunkName: "newAddressSelectorView" */ "../views/old/newAddressSelectorView.vue");

const OverflowDirectiveView = () =>
  import(/* webpackChunkName: "OverflowDirectiveView" */ "../views/old/OverflowDirectiveView.vue");

const PoolPopupView = () =>
  import(/* webpackChunkName: "PoolPopupView" */ "../views/old/PoolPopupView.vue");

const PoolWindowView = () =>
  import(/* webpackChunkName: "PoolWindowView" */ "../views/old/PoolWindowView.vue");

const ScrollIntoViewView = () =>
  import(/* webpackChunkName: "ScrollIntoViewView" */ "../views/old/ScrollIntoViewView.vue");

const scrollLoadListView = () =>
  import(/* webpackChunkName: "scrollLoadListView" */ "../views/old/scrollLoadListView.vue");

const SecondaryMeetingMenuView = () =>
  import(/* webpackChunkName: "SecondaryMeetingMenuView" */ "../views/old/SecondaryMeetingMenuView.vue");

const selectLoadMoreView = () =>
  import(/* webpackChunkName: "selectLoadMoreView" */ "../views/old/selectLoadMoreView.vue");

const selectorAddView = () =>
  import(/* webpackChunkName: "selectorAddView" */ "../views/old/selectorAddView.vue");

const TableDemo1View = () =>
  import(/* webpackChunkName: "TableDemo1View" */ "../views/old/TableDemo1View.vue");

const TimeTest1View = () =>
  import(/* webpackChunkName: "TimeTest1View" */ "../views/old/TimeTest1View.vue");

const UploaderView = () =>
  import(/* webpackChunkName: "UploaderView" */ "../views/old/UploaderView.vue");

const VanFormValidateView = () =>
  import(/* webpackChunkName: "VanFormValidateView" */ "../views/old/VanFormValidateView.vue");

const VanPopupInVancellView = () =>
  import(/* webpackChunkName: "VanPopupInVancellView" */ "../views/old/VanPopupInVancellView.vue");

const VantCalenderView = () =>
  import(/* webpackChunkName: "VantCalenderView" */ "../views/old/VantCalenderView.vue");

const VantCellCustomView = () =>
  import(/* webpackChunkName: "VantCellCustomView" */ "../views/old/VantCellCustomView.vue");

const VantMultiSelectorView = () =>
  import(/* webpackChunkName: "VantMultiSelectorView" */ "../views/old/VantMultiSelectorView.vue");

const virtualscroll2View = () =>
  import(/* webpackChunkName: "virtualscroll2View" */ "../views/old/virtualscroll2View.vue");

const virtualScrollView = () =>
  import(/* webpackChunkName: "virtualScrollView" */ "../views/old/virtualScrollView.vue");

const WebWorkerView = () =>
  import(/* webpackChunkName: "WebWorkerView" */ "../views/old/WebWorkerView.vue");

export const otherRoutes = [
  {
    path: "/HomeView",
    name: "HomeView",
    component: HomeView,
  },
  {
    path: "/new/hoverInfoView",
    name: "hoverInfoView",
    component: hoverInfoView,
  },
  {
    path: "/old/ApprovalLIstView",
    name: "ApprovalLIstView",
    component: ApprovalLIstView,
  },
  {
    path: "/old/buttonView",
    name: "buttonView",
    component: buttonView,
  },
  {
    path: "/old/customButtonView",
    name: "customButtonView",
    component: customButtonView,
  },
  {
    path: "/old/customCascaderView",
    name: "customCascaderView",
    component: customCascaderView,
  },
  {
    path: "/old/DayDiffView",
    name: "DayDiffView",
    component: DayDiffView,
  },
  {
    path: "/old/disableiosRubberBandEffectDemoView",
    name: "disableiosRubberBandEffectDemoView",
    component: disableiosRubberBandEffectDemoView,
  },
  {
    path: "/old/ElemTableValidateView",
    name: "ElemTableValidateView",
    component: ElemTableValidateView,
  },
  {
    path: "/old/ELForm1View",
    name: "ELForm1View",
    component: ELForm1View,
  },
  {
    path: "/old/ELForm2View",
    name: "ELForm2View",
    component: ELForm2View,
  },
  {
    path: "/old/ElForm3View",
    name: "ElForm3View",
    component: ElForm3View,
  },
  {
    path: "/old/ElMessageView",
    name: "ElMessageView",
    component: ElMessageView,
  },
  {
    path: "/old/FileDemoView",
    name: "FileDemoView",
    component: FileDemoView,
  },
  {
    path: "/old/FileNameDemoView",
    name: "FileNameDemoView",
    component: FileNameDemoView,
  },
  {
    path: "/old/FormDemo1View",
    name: "FormDemo1View",
    component: FormDemo1View,
  },
  {
    path: "/old/FormDisabledStyleView",
    name: "FormDisabledStyleView",
    component: FormDisabledStyleView,
  },
  {
    path: "/old/formValidateView",
    name: "formValidateView",
    component: formValidateView,
  },
  {
    path: "/old/handwrittenTreeView",
    name: "handwrittenTreeView",
    component: handwrittenTreeView,
  },
  {
    path: "/old/newAddressSelectorView",
    name: "newAddressSelectorView",
    component: newAddressSelectorView,
  },
  {
    path: "/old/OverflowDirectiveView",
    name: "OverflowDirectiveView",
    component: OverflowDirectiveView,
  },
  {
    path: "/old/PoolPopupView",
    name: "PoolPopupView",
    component: PoolPopupView,
  },
  {
    path: "/old/PoolWindowView",
    name: "PoolWindowView",
    component: PoolWindowView,
  },
  {
    path: "/old/ScrollIntoViewView",
    name: "ScrollIntoViewView",
    component: ScrollIntoViewView,
  },
  {
    path: "/old/scrollLoadListView",
    name: "scrollLoadListView",
    component: scrollLoadListView,
  },
  {
    path: "/old/SecondaryMeetingMenuView",
    name: "SecondaryMeetingMenuView",
    component: SecondaryMeetingMenuView,
  },
  {
    path: "/old/selectLoadMoreView",
    name: "selectLoadMoreView",
    component: selectLoadMoreView,
  },
  {
    path: "/old/selectorAddView",
    name: "selectorAddView",
    component: selectorAddView,
  },
  {
    path: "/old/TableDemo1View",
    name: "TableDemo1View",
    component: TableDemo1View,
  },
  {
    path: "/old/TimeTest1View",
    name: "TimeTest1View",
    component: TimeTest1View,
  },
  {
    path: "/old/UploaderView",
    name: "UploaderView",
    component: UploaderView,
  },
  {
    path: "/old/VanFormValidateView",
    name: "VanFormValidateView",
    component: VanFormValidateView,
  },
  {
    path: "/old/VanPopupInVancellView",
    name: "VanPopupInVancellView",
    component: VanPopupInVancellView,
  },
  {
    path: "/old/VantCalenderView",
    name: "VantCalenderView",
    component: VantCalenderView,
  },
  {
    path: "/old/VantCellCustomView",
    name: "VantCellCustomView",
    component: VantCellCustomView,
  },
  {
    path: "/old/VantMultiSelectorView",
    name: "VantMultiSelectorView",
    component: VantMultiSelectorView,
  },
  {
    path: "/old/virtualscroll2View",
    name: "virtualscroll2View",
    component: virtualscroll2View,
  },
  {
    path: "/old/virtualScrollView",
    name: "virtualScrollView",
    component: virtualScrollView,
  },
  {
    path: "/old/WebWorkerView",
    name: "WebWorkerView",
    component: WebWorkerView,
  }
];
