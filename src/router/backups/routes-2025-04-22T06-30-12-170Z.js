const buttonView = () =>
  import(/* webpackChunkName: "button" */ "../views/new/buttonView.vue");
const ApprovalLIstView = () =>
  import(
    /* webpackChunkName: "ApprovalLIst" */ "../views/new/ApprovalLIstView.vue"
  );
const customCascaderView = () =>
  import(
    /* webpackChunkName: "customCascader" */ "../views/old/customCascaderView.vue"
  );
const customButtonView = () =>
  import(
    /* webpackChunkName: "customButton" */ "../views/old/customButtonView.vue"
  );

export const otherRoutes = [
  {
    path: "/new/ApprovalLIstView",
    name: "ApprovalLIstView",
    component: ApprovalLIstView,
  },
  {
    path: "/new/buttonView",
    name: "buttonView",
    component: buttonView,
  },
  {
    path: "/old/customButtonView",
    name: "customButtonView",
    component: customButtonView,
  },

  {
    path: "/old/customCascaderView",
    name: "customCascaderView",
    component: customCascaderView,
  },
];
