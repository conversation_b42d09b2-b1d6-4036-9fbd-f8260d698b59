const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

rl.question('请输入组件名称：', (componentName) => {
    rl.close();

    if (!componentName) {
        console.error('请提供组件名称');
        process.exit(1);
    }

    const componentPath = path.resolve(__dirname, `src/views/${componentName}View.vue`);

    const componentTemplate = `
<template>
  <div>
    <h1>${componentName}View</h1>
  </div>
</template>

<script>
export default {
  name: '${componentName}View',
};
</script>
<style scoped>
</style>
`;

    fs.writeFile(componentPath, componentTemplate, (err) => {
        if (err) throw err;
        console.log(`组件${componentName}View已创建`);
    });

// 更新路由文件
    const routerPath = path.resolve(__dirname, 'src/router/index.js');

    fs.readFile(routerPath, 'utf8', (err, data) => {
        if (err) throw err;

        const importLine = `const ${componentName}View = () => import(/* webpackChunkName: "${componentName}" */ '../views/${componentName}View.vue');\n`;
        const routeLine = `  ,{\n    path: '/${componentName}',\n    name: '${componentName}',\n    component: ${componentName}View\n  }\n`;

        const routeIndex = data.lastIndexOf(']');
        const newRoute = data.slice(0, routeIndex) + routeLine + data.slice(routeIndex);

        fs.writeFile(routerPath, importLine + newRoute, (err) => {
            if (err) throw err;
            console.log(`路由${componentName}已添加`);
        });
    });


// 更新首页菜单
    const appVuePath = path.resolve(__dirname, 'src/App.vue');
    const menuLine = `      <el-menu-item index="${componentName}">\n        <router-link to="/${componentName}">${componentName}</router-link>\n      </el-menu-item>\n`;

    fs.readFile(appVuePath, 'utf8', (err, data) => {
        if (err) throw err;

        const menuIndex = data.lastIndexOf('</el-menu>');
        const newMenu = data.slice(0, menuIndex) + menuLine + data.slice(menuIndex);

        fs.writeFile(appVuePath, newMenu, (err) => {
            if (err) throw err;
            console.log(`菜单项${componentName}已添加到首页`);
        });
    });

});


